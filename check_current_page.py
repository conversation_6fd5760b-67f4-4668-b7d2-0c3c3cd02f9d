#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import xml.etree.ElementTree as ET

def check_private_user():
    """检测当前页面是否为私密用户"""
    
    try:
        # 执行雷电命令获取UI XML
        cmd = r'G:\leidian\LDPlayer9\ldconsole.exe action2 --index 2 --key call.uiautomator --value "uiautomator dump /sdcard/current_page.xml && cat /sdcard/current_page.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout:
            xml_content = result.stdout.strip()
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 私密检测关键词
            private_keywords = ["私密账户", "私密账号", "私人帐户", "此帐户为私人帐户", "Private", "private", "This Account is Private"]
            
            # 查找私密标识
            found_private = False
            private_text = ""
            
            for elem in root.iter():
                elem_text = elem.get('text', '').strip()
                if elem_text:
                    for keyword in private_keywords:
                        if keyword in elem_text:
                            found_private = True
                            private_text = elem_text
                            break
                    if found_private:
                        break
            
            # 返回结果
            if found_private:
                print(f"✅ 检测结果：当前页面是私密用户")
                print(f"🔍 检测到的文本：'{private_text}'")
            else:
                print("❌ 检测结果：当前页面不是私密用户")
                
        else:
            print("❌ 无法获取页面信息")
            
    except Exception as e:
        print(f"❌ 检测失败：{e}")

if __name__ == "__main__":
    check_private_user()
