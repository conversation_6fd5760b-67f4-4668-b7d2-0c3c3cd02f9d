#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 统一模拟器管理器 - 整合所有模拟器操作
========================================
功能描述: 统一管理模拟器的所有操作，消除重复逻辑

模块结构:
1. 数据模型定义 (StartupTask, StartupProgress)
2. 启动管理器 (EmulatorStartupManager)
3. 统一模拟器管理器 (UnifiedEmulatorManager)
4. 全局实例管理

主要功能:
- 模拟器启动/停止: 批量操作、并发控制、重试机制
- 状态管理: 实时状态监控、进度统计、信号通知
- 命令执行: 异步命令执行、错误处理、编码兼容
- 数据同步: 数据库操作、模拟器扫描、信息缓存

调用关系: 被异步桥梁和UI层调用，替代分散的管理逻辑
注意事项: 基于任务队列的并发控制，确保线程安全和资源管理
========================================
"""

import asyncio
import logging
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional
import time
from PyQt6.QtCore import QObject, pyqtSignal
from dataclasses import dataclass

from .simple_config import get_config_manager
from .logger_manager import log_emulator, log_performance, log_error
from .status_converter import StatusConverter
from data.repositories.emulator_repository import get_emulator_repository
from data.models.emulator_model import EmulatorModel

# 🎯 使用统一状态转换器，删除重复的状态定义
from .status_converter import EmulatorStatus


# ============================================================================
# 🎯 1. 数据模型定义
# ============================================================================
# 功能描述: 定义启动任务和进度统计的数据结构
# 调用关系: 被启动管理器使用，管理任务状态和进度信息
# 注意事项: 数据类使用dataclass装饰器，确保类型安全和默认值设置
# ============================================================================

@dataclass
class StartupTask:
    """启动任务对象"""
    emulator_id: int
    current_attempt: int = 1
    start_time: float = 0.0
    state: str = EmulatorStatus.QUEUED
    error_message: str = ""
    cancelled: bool = False


@dataclass
class StartupProgress:
    """启动进度统计"""
    total_count: int = 0
    queued_count: int = 0
    starting_count: int = 0
    running_count: int = 0
    failed_count: int = 0
    cancelled_count: int = 0

    @property
    def completed_count(self) -> int:
        return self.running_count + self.failed_count + self.cancelled_count

    @property
    def progress_percentage(self) -> float:
        if self.total_count == 0:
            return 0.0
        return (self.completed_count / self.total_count) * 100


# ============================================================================
# 🎯 2. 启动管理器
# ============================================================================
# 功能描述: 专门负责模拟器启动流程的管理，包括队列、并发控制、重试机制
# 调用关系: 被统一模拟器管理器调用，处理所有启动相关的复杂逻辑
# 注意事项: 使用异步调度器控制启动间隔，确保并发限制和资源管理
# ============================================================================

class EmulatorStartupManager(QObject):
    """🎯 统一启动管理器 - 集中管理所有启动相关逻辑"""

    # 🎯 信号定义
    emulator_state_changed = pyqtSignal(int, str, str)  # id, old_state, new_state
    batch_state_changed = pyqtSignal(list, str, str)  # emulator_ids, old_state, new_state
    startup_progress = pyqtSignal(dict)  # progress_info
    batch_startup_completed = pyqtSignal(dict)  # result_summary

    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 队列管理
        self.startup_queue: List[int] = []

        # 🎯 并发控制
        self.active_startups: Dict[int, StartupTask] = {}  # 正在启动过程中的模拟器
        self.running_emulators: Dict[int, float] = {}  # 已启动成功的模拟器 {id: start_time}

        # 🎯 时间控制
        self.last_command_time: float = 0.0

        # 🎯 状态管理
        self.emulator_states: Dict[int, str] = {}

        # 🎯 进度统计
        self.progress = StartupProgress()

        # 🎯 调度器控制
        self.scheduler_running = False
        self.scheduler_task: Optional[asyncio.Task] = None

        log_emulator("统一启动管理器初始化完成", component="StartupManager")

    # ------------------------------------------------------------------------
    # 🎯 2.1 配置管理
    # ------------------------------------------------------------------------
    # 功能描述: 提供配置参数的统一访问接口
    # 调用关系: 被启动流程各个环节调用，获取超时、重试、并发等配置
    # 注意事项: 提供默认值确保系统稳定运行
    # ------------------------------------------------------------------------

    def get_config(self, key: str, default: Any) -> Any:
        """获取配置参数"""
        return self.config_manager.get(key, default)

    # ------------------------------------------------------------------------
    # 🎯 2.2 批量启动接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供批量和单个模拟器启动的统一接口
    # 调用关系: 被统一模拟器管理器调用，作为启动操作的入口点
    # 注意事项: 自动管理队列和调度器，确保启动流程的正确执行
    # ------------------------------------------------------------------------

    async def start_batch(self, emulator_ids: List[int]) -> Dict[str, Any]:
        """🎯 批量启动模拟器 - 统一入口"""
        if not emulator_ids:
            return {'status': 'failed', 'message': '模拟器列表为空'}

        log_emulator(f"批量启动请求", count=len(emulator_ids), component="StartupManager")

        # 🎯 添加到启动队列 - 批量处理避免重复日志
        queued_emulators = []
        for emulator_id in emulator_ids:
            if emulator_id not in self.startup_queue and emulator_id not in self.active_startups:
                self.startup_queue.append(emulator_id)
                old_state = self.emulator_states.get(emulator_id, "未知")
                self.emulator_states[emulator_id] = EmulatorStatus.QUEUED
                queued_emulators.append(emulator_id)

        # 🎯 发射批量状态变化信号，避免多条重复日志
        if queued_emulators:
            self.batch_state_changed.emit(queued_emulators, "未知", EmulatorStatus.QUEUED)

        # 🎯 更新进度统计
        self.progress.total_count = len(emulator_ids)
        self._update_progress_statistics()

        # 🎯 启动调度器
        if not self.scheduler_running:
            await self._start_scheduler()

        return {
            'status': 'queued',
            'total': len(emulator_ids),
            'message': f'已将 {len(emulator_ids)} 个模拟器添加到启动队列'
        }

    async def start_single(self, emulator_id: int) -> Dict[str, Any]:
        """🎯 单个模拟器启动 - 统一入口"""
        return await self.start_batch([emulator_id])

    # ------------------------------------------------------------------------
    # 🎯 2.3 取消操作接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供取消启动操作的接口，支持单个和批量取消
    # 调用关系: 被用户操作或异常处理流程调用，停止启动过程
    # 注意事项: 需要正确清理资源和更新状态，避免资源泄漏
    # ------------------------------------------------------------------------

    async def cancel_startup(self, emulator_id: int):
        """🎯 取消单个模拟器启动"""
        if emulator_id in self.startup_queue:
            self.startup_queue.remove(emulator_id)
            self._update_emulator_state(emulator_id, EmulatorStatus.CANCELLED)

        elif emulator_id in self.active_startups:
            task = self.active_startups[emulator_id]
            task.cancelled = True
            await self._force_close_emulator(emulator_id)
            del self.active_startups[emulator_id]
            self._update_emulator_state(emulator_id, EmulatorStatus.CANCELLED)

        elif emulator_id in self.running_emulators:
            # 🎯 取消正在运行的模拟器
            await self._force_close_emulator(emulator_id)
            del self.running_emulators[emulator_id]
            self._update_emulator_state(emulator_id, EmulatorStatus.CANCELLED)

    async def cancel_all_startup(self):
        """🎯 取消所有启动 - 完全参考批量启动的方法"""
        # 🎯 收集所有需要取消的模拟器ID - 批量处理避免重复日志
        cancelled_emulators = []

        # 清空队列
        for emulator_id in self.startup_queue:
            cancelled_emulators.append(emulator_id)
            self.emulator_states[emulator_id] = EmulatorStatus.CANCELLED
        self.startup_queue.clear()

        # 取消正在启动的
        for emulator_id in list(self.active_startups.keys()):
            cancelled_emulators.append(emulator_id)
            task = self.active_startups[emulator_id]
            task.cancelled = True
            await self._force_close_emulator(emulator_id)
            del self.active_startups[emulator_id]
            self.emulator_states[emulator_id] = EmulatorStatus.CANCELLED

        # 🎯 发射批量状态变化信号，避免多条重复日志（完全参考批量启动）
        if cancelled_emulators:
            log_emulator(f"批量取消请求", count=len(cancelled_emulators), component="StartupManager")
            self.batch_state_changed.emit(cancelled_emulators, "排队中", EmulatorStatus.CANCELLED)

        # 停止调度器
        if self.scheduler_task:
            self.scheduler_task.cancel()
            self.scheduler_running = False

    # ------------------------------------------------------------------------
    # 🎯 2.4 调度器管理
    # ------------------------------------------------------------------------
    # 功能描述: 管理启动调度器的生命周期，控制启动时机和并发
    # 调用关系: 被批量启动流程调用，自动管理启动队列的执行
    # 注意事项: 使用异步任务避免阻塞，确保调度器的正确启停
    # ------------------------------------------------------------------------

    async def _start_scheduler(self):
        """🎯 启动主调度器"""
        if self.scheduler_running:
            return

        self.scheduler_running = True
        self.scheduler_task = asyncio.create_task(self._main_scheduler_loop())
        log_emulator("启动调度器已启动", component="StartupManager")

    # ------------------------------------------------------------------------
    # 🎯 2.5 核心调度循环
    # ------------------------------------------------------------------------
    # 功能描述: 主调度循环，统一控制启动间隔、并发限制和队列处理
    # 调用关系: 被调度器管理模块调用，是启动流程的核心控制逻辑
    # 注意事项: 循环中包含多个检查点，确保启动流程的稳定和可控
    # ------------------------------------------------------------------------

    async def _main_scheduler_loop(self):
        try:
            while self.scheduler_running:
                # 🎯 第1步：检查是否有待处理的模拟器（修复并发控制问题）
                total_pending = len(self.startup_queue) + len(self.active_startups)
                if total_pending == 0:
                    break  # 真正的所有任务完成

                # 如果队列为空但有正在启动的模拟器，等待它们完成
                if not self.startup_queue:
                    await asyncio.sleep(1)
                    continue

                # 🎯 第2步：检查并发限制
                max_concurrent = self.get_config("max_concurrent_tasks", 2)
                total_active = len(self.active_startups) + len(self.running_emulators)
                if total_active >= max_concurrent:
                    # 移除并发已满的日志噪音 - 这是正常的等待状态
                    await asyncio.sleep(1)  # 等待槽位释放
                    continue

                # 🎯 第3步：检查启动命令间隔
                command_interval = self.get_config("start_interval", 3)
                if time.time() - self.last_command_time < command_interval:
                    await asyncio.sleep(0.5)  # 等待间隔满足
                    continue

                # 🎯 第4步：发送启动命令
                emulator_id = self.startup_queue.pop(0)
                await self._send_startup_command(emulator_id)
                self.last_command_time = time.time()

        except asyncio.CancelledError:
            log_emulator("调度器被取消", component="StartupManager")
        except Exception as e:
            log_error(f"调度器异常: {e}")
        finally:
            self.scheduler_running = False
            log_emulator("调度器已停止", component="StartupManager")

    # ------------------------------------------------------------------------
    # 🎯 2.6 启动执行流程
    # ------------------------------------------------------------------------
    # 功能描述: 执行具体的启动命令和启动过程，包括重试和状态管理
    # 调用关系: 被调度循环调用，处理单个模拟器的完整启动流程
    # 注意事项: 包含异步启动过程，避免阻塞调度器，支持并发启动
    # -----------------------------------------------------------------------
    async def _send_startup_command(self, emulator_id: int):
        """🎯 发送启动命令并开始启动过程"""
        # 🎯 创建启动任务
        task = StartupTask(emulator_id, current_attempt=1)
        self.active_startups[emulator_id] = task

        # 🎯 更新状态为"启动中"
        self._update_emulator_state(emulator_id, EmulatorStatus.STARTING)

        # 🎯 异步启动启动过程（不阻塞调度器）
        asyncio.create_task(self._startup_process(emulator_id))

    async def _startup_process(self, emulator_id: int):
        """🎯 独立的启动过程 - 包含重试逻辑"""
        task = self.active_startups[emulator_id]

        startup_timeout = self.get_config("start_timeout", 12)
        max_retry_count = self.get_config("start_fail_limit", 2)

        # 🎯 重试循环
        for attempt in range(1, max_retry_count + 1):
            if task.cancelled:
                return

            task.current_attempt = attempt

            try:
                log_emulator(f"第 {attempt}/{max_retry_count} 次启动尝试", emulator_id=emulator_id)

                # 🎯 执行启动命令
                success, message = await self._execute_ldconsole_launch(emulator_id)
                if not success:
                    if attempt >= max_retry_count:
                        await self._handle_startup_failed(emulator_id, "启动命令失败", message)
                        return
                    await asyncio.sleep(2)  # 重试间隔
                    continue

                # 🎯 等待启动完成检测
                success = await self._wait_for_android_ready(emulator_id, startup_timeout)
                if success:
                    await self._handle_startup_success(emulator_id)
                    return
                else:
                    # 启动超时，强制关闭
                    await self._force_close_emulator(emulator_id)
                    if attempt >= max_retry_count:
                        await self._handle_startup_failed(emulator_id, "启动超时", f"超时{startup_timeout}秒")
                        return
                    await asyncio.sleep(2)  # 重试间隔

            except Exception as e:
                if attempt >= max_retry_count:
                    await self._handle_startup_failed(emulator_id, "启动异常", str(e))
                    return
                await asyncio.sleep(2)

    # ------------------------------------------------------------------------
    # 🎯 2.7 底层命令执行
    # ------------------------------------------------------------------------
    # 功能描述: 执行底层的ldconsole命令和系统检测，处理命令执行细节
    # 调用关系: 被启动流程调用，提供命令执行和状态检测的基础能力
    # 注意事项: 需要处理命令执行异常和超时，确保系统稳定性
    # ------------------------------------------------------------------------

    async def _execute_ldconsole_launch(self, emulator_id: int) -> Tuple[bool, str]:
        """🎯 执行ldconsole启动命令"""
        try:
            # 🎯 获取UnifiedEmulatorManager实例来调用_execute_command_async
            manager = self.parent()
            if hasattr(manager, '_execute_command_async'):
                return await manager._execute_command_async(['launch', '--index', str(emulator_id)])
            else:
                return False, "无法访问命令执行方法"
        except Exception as e:
            return False, str(e)

    async def _wait_for_android_ready(self, emulator_id: int, timeout: int) -> bool:
        """🎯 等待Android系统启动完成"""
        start_time = time.time()
        check_interval = 3  # 固定3秒检测间隔

        log_emulator(f"等待Android系统启动完成", emulator_id=emulator_id)

        while time.time() - start_time < timeout:
            if self.active_startups[emulator_id].cancelled:
                return False

            try:
                # 🎯 获取UnifiedEmulatorManager实例来调用get_emulator_status
                manager = self.parent()
                if hasattr(manager, 'get_emulator_status'):
                    is_running, is_android = await manager.get_emulator_status(emulator_id)
                    if is_running and is_android:
                        elapsed_time = time.time() - start_time
                        log_emulator(f"Android系统启动完成", emulator_id=emulator_id, elapsed_time=f"{elapsed_time:.1f}秒")
                        return True

            except Exception as e:
                log_error(f"检测Android状态异常: {e}")

            await asyncio.sleep(check_interval)

        return False

    async def _force_close_emulator(self, emulator_id: int):
        """🎯 强制关闭模拟器"""
        try:
            manager = self.parent()
            if hasattr(manager, '_execute_command_async'):
                await manager._execute_command_async(['quit', '--index', str(emulator_id)])
                log_emulator(f"强制关闭模拟器", emulator_id=emulator_id)
        except Exception as e:
            log_error(f"强制关闭模拟器失败: {e}")

    # ------------------------------------------------------------------------
    # 🎯 2.8 启动结果处理
    # ------------------------------------------------------------------------
    # 功能描述: 处理启动成功和失败的结果，更新状态和释放资源
    # 调用关系: 被启动流程调用，处理启动过程的最终结果
    # 注意事项: 需要正确更新状态和发送信号，确保UI和其他组件同步
    # ------------------------------------------------------------------------

    async def _handle_startup_success(self, emulator_id: int):
        """🎯 处理启动成功"""
        # 🎯 从启动中移除，添加到运行中（保持并发控制）
        if emulator_id in self.active_startups:
            del self.active_startups[emulator_id]
        self.running_emulators[emulator_id] = time.time()  # 记录启动完成时间

        self._update_emulator_state(emulator_id, EmulatorStatus.RUNNING)
        log_emulator(f"模拟器启动成功", emulator_id=emulator_id,
                    running_count=len(self.running_emulators))

        # 🎯 更新心跳活动时间 - 启动成功视为一次心跳活动
        from .heartbeat_manager import update_emulator_heartbeat_activity
        update_emulator_heartbeat_activity(emulator_id)

        # 🎯 获取运行时状态信息并发送详细状态更新信号
        if hasattr(self.parent(), '_get_runtime_info'):
            runtime_info = await self.parent()._get_runtime_info(emulator_id)
            self.parent().emulator_state_changed_detailed.emit(emulator_id, EmulatorStatus.STARTING, EmulatorStatus.RUNNING, runtime_info)

    async def _handle_startup_failed(self, emulator_id: int, error_type: str, message: str):
        """🎯 处理启动失败"""
        if emulator_id in self.active_startups:
            del self.active_startups[emulator_id]
        self._update_emulator_state(emulator_id, EmulatorStatus.FAILED)
        log_error(f"模拟器{emulator_id}启动失败: {error_type} - {message}")

    # ------------------------------------------------------------------------
    # 🎯 2.9 资源管理
    # ------------------------------------------------------------------------
    # 功能描述: 管理模拟器槽位的分配和释放，维护并发控制
    # 调用关系: 被启动成功/失败处理和外部停止操作调用
    # 注意事项: 槽位释放后需要检查是否可以启动下一个任务
    # ------------------------------------------------------------------------

    def release_emulator_slot(self, emulator_id: int):
        """🎯 释放模拟器槽位 - 当模拟器停止或任务完成时调用"""
        if emulator_id in self.running_emulators:
            del self.running_emulators[emulator_id]
            log_emulator(f"释放模拟器槽位", emulator_id=emulator_id,
                        remaining_running=len(self.running_emulators),
                        component="StartupManager")

            # 🎯 释放槽位后，尝试启动下一个任务
            if not self.scheduler_running and self.startup_queue:
                asyncio.create_task(self._start_scheduler())

    # ------------------------------------------------------------------------
    # 🎯 2.10 状态和进度管理
    # ------------------------------------------------------------------------
    # 功能描述: 管理模拟器状态变化和启动进度统计，发送相关信号
    # 调用关系: 被启动流程各个环节调用，维护状态一致性
    # 注意事项: 状态变化需要发送信号通知UI，进度统计要实时更新
    # ------------------------------------------------------------------------

    def _update_emulator_state(self, emulator_id: int, new_state: str):
        """🎯 更新模拟器状态"""
        old_state = self.emulator_states.get(emulator_id, "未知")
        self.emulator_states[emulator_id] = new_state

        # 🎯 发送状态变化信号
        self.emulator_state_changed.emit(emulator_id, old_state, new_state)

        # 🎯 更新统计信息
        self._update_progress_statistics()

    def _update_progress_statistics(self):
        """🎯 更新进度统计"""
        self.progress.queued_count = len(self.startup_queue)
        self.progress.starting_count = len(self.active_startups)

        # 🎯 使用实际运行中的模拟器数量（从running_emulators获取）
        self.progress.running_count = len(self.running_emulators)
        self.progress.failed_count = sum(1 for state in self.emulator_states.values() if state == EmulatorStatus.FAILED)
        self.progress.cancelled_count = sum(1 for state in self.emulator_states.values() if state == EmulatorStatus.CANCELLED)

        # 🎯 发送进度信号
        progress_info = {
            'total': self.progress.total_count,
            'queued': self.progress.queued_count,
            'starting': self.progress.starting_count,
            'running': self.progress.running_count,
            'failed': self.progress.failed_count,
            'cancelled': self.progress.cancelled_count,
            'completed': self.progress.completed_count,
            'percentage': self.progress.progress_percentage,
            'concurrent_slots_used': len(self.active_startups) + len(self.running_emulators),
            'max_concurrent': self.get_config("max_concurrent_tasks", 2)
        }
        self.startup_progress.emit(progress_info)


# ============================================================================
# 🎯 3. 统一模拟器管理器
# ============================================================================
# 功能描述: 统一管理所有模拟器操作，集成启动管理器和其他功能模块
# 调用关系: 被异步桥梁调用，作为模拟器操作的统一入口点
# 注意事项: 集成多个子管理器，提供完整的模拟器生命周期管理
# ============================================================================

class UnifiedEmulatorManager(QObject):
    """🎯 统一模拟器管理器 - 集成统一启动管理器"""

    # 🎯 保持现有信号兼容性
    task_finished = pyqtSignal(int, int, str, dict)  # 模拟器ID、行号、任务名、额外参数
    emulator_state_changed_detailed = pyqtSignal(int, str, str, dict)  # 模拟器ID，旧状态，新状态，详细信息

    def __init__(self):
        super().__init__()
        self.config_manager = get_config_manager()
        self.logger = logging.getLogger(self.__class__.__name__)

        # ========================================================================
        # 🎯 1. 核心组件初始化
        # ========================================================================

        # 🎯 数据库仓库
        self.emulator_repo = get_emulator_repository()

        # 🎯 启动管理器
        self.startup_manager = EmulatorStartupManager(self.config_manager, self)

        # 🎯 心跳监控管理器
        from .heartbeat_manager import get_simple_heartbeat_manager
        self.heartbeat_manager = get_simple_heartbeat_manager()

        # ========================================================================
        # 🎯 2. 信号连接
        # ========================================================================

        # 🎯 启动管理器信号连接
        self.startup_manager.emulator_state_changed.connect(self._on_emulator_state_changed)
        self.startup_manager.startup_progress.connect(self._on_startup_progress)
        self.startup_manager.batch_startup_completed.connect(self._on_batch_startup_completed)

        # 🎯 心跳管理器信号连接
        self.heartbeat_manager.heartbeat_success.connect(self._on_heartbeat_success)
        self.heartbeat_manager.heartbeat_failed.connect(self._on_heartbeat_failed)
        self.heartbeat_manager.emulator_recovered.connect(self._on_emulator_recovered)
        self.heartbeat_manager.emulator_switched.connect(self._on_emulator_switched)

        # ========================================================================
        # 🎯 3. 服务启动
        # ========================================================================

        # 🎯 启动心跳监控服务
        self.heartbeat_manager.start_monitoring()

        # 🎯 原生API实例（延迟初始化）
        self._native_api = None

        log_emulator("统一模拟器管理器初始化完成", component="UnifiedEmulatorManager")

    # ------------------------------------------------------------------------
    # 🎯 3.1 信号处理
    # ------------------------------------------------------------------------
    # 功能描述: 处理来自启动管理器的信号，转换为UI层可识别的格式
    # 调用关系: 被启动管理器的信号触发，向UI层发送状态变化通知
    # 注意事项: 需要保持信号格式的兼容性，确保UI层正确接收
    # ------------------------------------------------------------------------

    def _on_emulator_state_changed(self, emulator_id: int, old_state: str, new_state: str):
        """🎯 处理模拟器状态变化信号"""
        log_emulator(f"模拟器状态变化", emulator_id=emulator_id, old_state=old_state, new_state=new_state)

        # 🎯 心跳监控管理 - 根据状态变化启动或停止心跳监控
        if new_state == EmulatorStatus.RUNNING:
            # 模拟器启动成功，添加到心跳监控
            self.heartbeat_manager.add_emulator_monitoring(emulator_id)
            # 如果心跳监控服务未启动，则启动它
            if not self.heartbeat_manager.running:
                self.heartbeat_manager.start_monitoring()
        elif new_state in [EmulatorStatus.STOPPED, EmulatorStatus.FAILED, EmulatorStatus.CANCELLED]:
            # 模拟器停止、失败或取消，从心跳监控移除
            self.heartbeat_manager.remove_emulator_monitoring(emulator_id)

        # 🎯 转换为现有的task_finished信号格式，保持兼容性
        if new_state == EmulatorStatus.RUNNING:
            print(f"[UnifiedEmulatorManager] 发射task_finished信号: 模拟器{emulator_id}启动成功")
            self.task_finished.emit(emulator_id, 0, 'start', {'result': '启动成功', 'message': '启动完成'})
        elif new_state == EmulatorStatus.FAILED:
            self.task_finished.emit(emulator_id, 0, 'start', {'result': '启动失败', 'message': '启动失败'})
        elif new_state == EmulatorStatus.CANCELLED:
            self.task_finished.emit(emulator_id, 0, 'start', {'result': '已取消', 'message': '用户取消'})

    def _on_startup_progress(self, progress_info: dict):
        """🎯 处理启动进度信号"""
        log_emulator("启动进度更新", **progress_info, component="UnifiedEmulatorManager")

    def _on_batch_startup_completed(self, result_summary: dict):
        """🎯 处理批量启动完成信号"""
        log_emulator("批量启动完成", **result_summary, component="UnifiedEmulatorManager")

    def _on_heartbeat_success(self, emulator_id: int):
        """🎯 处理心跳成功信号"""
        log_emulator(f"心跳检测正常", emulator_id=emulator_id, component="UnifiedEmulatorManager")

    def _on_heartbeat_failed(self, emulator_id: int, error_msg: str, failure_count: int):
        """🎯 处理心跳失败信号"""
        log_emulator(f"心跳检测失败: {error_msg}, 失败次数: {failure_count}",
                    emulator_id=emulator_id, component="UnifiedEmulatorManager")

    def _on_emulator_recovered(self, emulator_id: int):
        """🎯 处理模拟器恢复信号"""
        log_emulator(f"模拟器已恢复正常", emulator_id=emulator_id, component="UnifiedEmulatorManager")

    def _on_emulator_switched(self, old_emulator_id: int, new_emulator_id: int):
        """🎯 处理模拟器切换信号"""
        log_emulator(f"模拟器已切换: {old_emulator_id} -> {new_emulator_id}",
                    component="UnifiedEmulatorManager")

    # ------------------------------------------------------------------------
    # 🎯 3.2 启动操作接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供模拟器启动操作的统一接口，委托给启动管理器
    # 调用关系: 被异步桥梁调用，作为启动操作的入口点
    # 注意事项: 所有启动逻辑委托给专门的启动管理器处理
    # ------------------------------------------------------------------------

    async def start_emulator(self, emulator_id: int) -> Dict[str, Any]:
        """🎯 启动单个模拟器 - 委托给统一启动管理器"""
        return await self.startup_manager.start_single(emulator_id)

    async def batch_start_emulators(self, emulator_ids: List[int]) -> Dict[str, Any]:
        """🎯 批量启动模拟器 - 委托给统一启动管理器"""
        return await self.startup_manager.start_batch(emulator_ids)

    async def cancel_emulator_startup(self, emulator_id: int):
        """🎯 取消模拟器启动 - 委托给统一启动管理器"""
        await self.startup_manager.cancel_startup(emulator_id)

    async def cancel_all_startup(self):
        """🎯 取消所有启动 - 委托给统一启动管理器"""
        await self.startup_manager.cancel_all_startup()

    async def close_all_tasks(self) -> Dict[str, Any]:
        """🎯 关闭所有任务 - 批量取消队列+心跳+关闭所有模拟器"""
        try:
            log_emulator("开始关闭所有任务和模拟器", component="UnifiedEmulatorManager")

            # 1. 停止心跳监控
            from .heartbeat_manager import get_simple_heartbeat_manager
            heartbeat_manager = get_simple_heartbeat_manager()
            heartbeat_manager.stop_monitoring()
            log_emulator("心跳监控已停止", component="UnifiedEmulatorManager")

            # 2. 取消所有启动任务和队列
            await self.startup_manager.cancel_all_startup()
            log_emulator("所有启动任务已取消", component="UnifiedEmulatorManager")

            # 3. 使用雷电官方quitall命令批量关闭所有模拟器
            success, message = await self._execute_command_async(['quitall'])

            if success:
                # 4. 清理所有运行状态
                self.startup_manager.running_emulators.clear()
                self.startup_manager.emulator_states.clear()
                log_emulator("所有模拟器已关闭，状态已清理", component="UnifiedEmulatorManager")
                return {'status': 'success', 'message': '所有任务和模拟器已关闭'}
            else:
                log_error(f"批量关闭模拟器失败: {message}", component="UnifiedEmulatorManager")
                return {'status': 'failed', 'message': f'关闭模拟器失败: {message}'}

        except Exception as e:
            log_error(f"关闭所有任务失败: {e}", component="UnifiedEmulatorManager")
            return {'status': 'failed', 'message': f'关闭所有任务失败: {str(e)}'}

    def get_native_api(self):
        """🎯 获取雷电模拟器原生API实例"""
        if self._native_api is None:
            try:
                from .native.base_api import LeiDianNativeAPI
                # 从配置获取雷电模拟器路径
                ld_base_path = self.config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
                self._native_api = LeiDianNativeAPI(ld_base_path)
                log_emulator("雷电模拟器原生API初始化成功", component="UnifiedEmulatorManager")
            except Exception as e:
                log_error(f"初始化雷电模拟器原生API失败: {e}", component="UnifiedEmulatorManager")
                return None
        return self._native_api

    def release_emulator_slot(self, emulator_id: int):
        """🎯 释放模拟器槽位 - 当模拟器停止或任务完成时调用"""
        self.startup_manager.release_emulator_slot(emulator_id)

    # ------------------------------------------------------------------------
    # 🎯 3.3 停止操作接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供模拟器停止操作的接口，包括单个和批量停止
    # 调用关系: 被异步桥梁调用，处理模拟器的停止请求
    # 注意事项: 停止后需要释放槽位，确保资源正确回收
    # ------------------------------------------------------------------------

    async def stop_emulator(self, emulator_id: int) -> Dict[str, Any]:
        """🎯 停止单个模拟器"""
        try:
            success, message = await self._execute_command_async(['quit', '--index', str(emulator_id)])

            if success:
                # 🎯 释放模拟器槽位
                self.release_emulator_slot(emulator_id)

                log_emulator(f"模拟器停止成功", emulator_id=emulator_id)
                self.task_finished.emit(emulator_id, 0, 'stop', {'result': '停止成功', 'message': message})
                return {'status': 'success', 'message': message}
            else:
                log_error(f"模拟器停止失败: {message}")
                self.task_finished.emit(emulator_id, 0, 'stop', {'result': '停止失败', 'message': message})
                return {'status': 'failed', 'message': message}

        except Exception as e:
            log_error(f"停止模拟器异常: {e}")
            self.task_finished.emit(emulator_id, 0, 'stop', {'result': '停止异常', 'message': str(e)})
            return {'status': 'error', 'message': str(e)}

    async def batch_stop_emulators(self, emulator_ids: List[int]) -> Dict[str, Any]:
        """🎯 批量停止模拟器"""
        start_time = time.time()
        log_emulator(f"批量停止请求", count=len(emulator_ids))

        success_count = 0
        failed_count = 0

        for emulator_id in emulator_ids:
            result = await self.stop_emulator(emulator_id)
            if result['status'] == 'success':
                success_count += 1
            else:
                failed_count += 1

        duration = time.time() - start_time
        log_performance(f"批量停止完成", duration, success=success_count, failed=failed_count, total=len(emulator_ids))

        return {
            'total': len(emulator_ids),
            'success': success_count,
            'failed': failed_count,
            'status': 'completed',
            'message': f'批量停止完成: 成功{success_count}个, 失败{failed_count}个',
            'duration': duration
        }

    # ------------------------------------------------------------------------
    # 🎯 3.4 命令执行引擎
    # ------------------------------------------------------------------------
    # 功能描述: 提供异步命令执行能力，处理ldconsole命令的执行和编码
    # 调用关系: 被启动、停止、状态检测等操作调用，提供底层命令支持
    # 注意事项: 需要处理Windows编码问题，确保命令执行的稳定性
    # ------------------------------------------------------------------------

    async def _execute_command_async(self, command: List[str]) -> Tuple[bool, str]:
        """🎯 参考代码的异步命令执行方式"""
        try:
            import asyncio
            from pathlib import Path

            # 获取模拟器路径
            emulator_path = self.config_manager.get("emulator_path", "")
            if not emulator_path:
                return False, "模拟器路径未设置"

            ldconsole_path = Path(emulator_path) / "ldconsole.exe"
            if not ldconsole_path.exists():
                return False, f"ldconsole.exe不存在: {ldconsole_path}"

            # 构建完整命令
            full_command = [str(ldconsole_path)] + command

            # 异步执行命令
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                creationflags=0x08000000  # CREATE_NO_WINDOW
            )

            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)

                if process.returncode == 0:
                    # 🎯 修复中文编码问题 - Windows系统使用GBK编码
                    try:
                        output = stdout.decode('gbk').strip()
                    except:
                        output = stdout.decode('utf-8', errors='ignore').strip()
                    return True, output
                else:
                    # 🎯 修复错误信息编码问题
                    try:
                        error_msg = stderr.decode('gbk').strip()
                        stdout_msg = stdout.decode('gbk').strip()
                    except:
                        error_msg = stderr.decode('utf-8', errors='ignore').strip()
                        stdout_msg = stdout.decode('utf-8', errors='ignore').strip()
                    return False, error_msg or stdout_msg

            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return False, "命令执行超时"

        except Exception as e:
            return False, f"命令执行异常: {e}"

    # ------------------------------------------------------------------------
    # 🎯 3.5 状态检测服务
    # ------------------------------------------------------------------------
    # 功能描述: 提供模拟器状态检测和运行时信息获取功能
    # 调用关系: 被启动流程和状态监控调用，检测模拟器运行状态
    # 注意事项: 使用雷电原生list2命令解析，确保状态检测的准确性
    # ------------------------------------------------------------------------

    async def get_emulator_status(self, emulator_id: int) -> Tuple[bool, bool]:
        """🎯 获取模拟器状态 - 统一使用list2解析（官方标准）"""
        try:
            # 🎯 统一验证逻辑：使用list2命令获取详细状态
            success, output = await self._execute_command_async(["list2"])
            if not success:
                return False, False

            # 🎯 严格解析list2输出，查找指定模拟器（官方10字段标准）
            for line in output.split('\n'):
                if line.strip() and ',' in line:
                    parts = line.split(',')
                    if len(parts) == 10:  # 🎯 官方标准：严格检查10个字段
                        try:
                            emu_id = int(parts[0])
                            if emu_id == emulator_id:
                                # 🎯 修复逻辑错误：正确解析list2字段
                                # parts[4] = 是否进入Android (0/1)
                                # parts[5] = 进程PID (-1表示未运行)
                                is_running = parts[5] != '-1'  # PID不为-1表示运行中
                                is_android = parts[4] == '1'   # Android状态字段

                                return is_running, is_android
                        except (ValueError, IndexError):
                            continue

            # 未找到指定模拟器
            return False, False

        except Exception as e:
            log_error(f"获取模拟器{emulator_id}状态失败: {e}")
            return False, False



    # ------------------------------------------------------------------------
    # 🎯 3.6 数据管理接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供模拟器数据的查询、扫描和管理功能
    # 调用关系: 被UI层和数据同步流程调用，管理模拟器信息
    # 注意事项: 数据操作需要与数据库保持同步，确保数据一致性
    # ------------------------------------------------------------------------

    async def batch_start(self, emulator_ids: List[int]) -> Dict[str, Any]:
        """批量启动模拟器 - 别名方法，委托给启动管理器"""
        return await self.startup_manager.start_batch(emulator_ids)

    # 🎯 删除重复的旧批量停止方法，使用新的实现
    
    def get_emulators(self) -> List[EmulatorModel]:
        """获取所有模拟器信息"""
        return self.emulator_repo.find_all()

    def get_emulator(self, emulator_id: int) -> Optional[EmulatorModel]:
        """获取指定模拟器信息"""
        return self.emulator_repo.find_by_id(emulator_id)

    async def scan_emulators(self, emulator_path: str, check_status: bool = False) -> List[EmulatorModel]:
        """扫描模拟器列表 - 快速版本，可选状态检查"""
        try:
            start_time = asyncio.get_event_loop().time()
            from .logger_manager import log_info
            log_info(f"开始快速扫描模拟器，路径: {emulator_path}, 检查状态: {check_status}")

            # 更新配置中的模拟器路径 - 使用扁平化配置
            self.config_manager.set("emulator_path", emulator_path)
            self.config_manager.save()

            # 验证路径
            from pathlib import Path
            ldconsole_path = Path(emulator_path) / "ldconsole.exe"
            if not ldconsole_path.exists():
                error_msg = f"ldconsole.exe不存在: {ldconsole_path}"
                log_error(error_msg)
                return []

            # 执行list命令获取模拟器列表
            success, output = await self._execute_command_async(["list2"])
            if not success:
                log_error(f"扫描模拟器失败: {output}")
                return []

            log_info(f"ldconsole命令执行成功，输出长度: {len(output)}")

            # 🎯 统一解析逻辑：严格按照官方10字段标准解析
            scanned_emulators = []
            for line in output.split('\n'):
                if line.strip() and ',' in line:
                    parts = line.split(',')
                    if len(parts) == 10:  # 🎯 官方标准：严格检查10个字段
                        try:
                            emulator_id = int(parts[0])
                            name = parts[1]
                            # 🎯 解析详细运行时信息（参考参考代码的完整解析）
                            top_hwnd = int(parts[2]) if parts[2] != '0' else -1  # 顶层句柄
                            bind_hwnd = int(parts[3]) if parts[3] != '0' else -1  # 绑定句柄
                            is_running = parts[4] == '1'  # 是否进入android
                            pid = int(parts[5]) if parts[5] != '-1' else -1  # 进程PID
                            vbox_pid = int(parts[6]) if parts[6] != '-1' else -1  # VBox进程PID
                            # 解析配置字段（官方文档标准）
                            width = int(parts[7])
                            height = int(parts[8])
                            dpi = int(parts[9])

                            # 🎯 根据运行状态设置初始状态
                            from core.status_converter import EmulatorStatus
                            initial_status = EmulatorStatus.RUNNING if is_running else EmulatorStatus.STOPPED

                            scanned_emulators.append({
                                'id': emulator_id,
                                'name': name,
                                'path': emulator_path,
                                'width': width,
                                'height': height,
                                'dpi': dpi,
                                'status': initial_status,  # 🎯 添加初始状态信息
                                # 🎯 添加运行时详细信息
                                'top_hwnd': top_hwnd,
                                'bind_hwnd': bind_hwnd,
                                'pid': pid,
                                'vbox_pid': vbox_pid
                                # 使用雷电原生API
                            })

                        except (ValueError, IndexError):
                            continue

            # 🎯 批量同步到数据库 - 性能优化（不包含状态信息）
            if scanned_emulators:
                # 创建不包含状态的数据用于数据库同步
                db_data = []
                for emu in scanned_emulators:
                    db_emu = emu.copy()
                    db_emu.pop('status', None)  # 移除状态信息，数据库不存储
                    db_data.append(db_emu)

                success = self.emulator_repo.batch_sync_from_scan(db_data)
                if not success:
                    log_error("模拟器数据批量同步到数据库失败")
                    return []

            duration = asyncio.get_event_loop().time() - start_time
            log_performance(f"快速模拟器扫描完成", duration, count=len(scanned_emulators))

            # 🎯 直接返回包含状态信息的扫描结果
            return scanned_emulators

        except Exception as e:
            log_error(f"扫描模拟器异常: {e}")
            return []

    async def _get_runtime_info(self, emulator_id: int) -> dict:
        """🎯 获取模拟器运行时状态信息"""
        try:
            # 使用list2命令获取详细信息
            success, output = await self._execute_command_async(["list2"])
            if not success:
                return {}

            # 解析list2输出，查找指定模拟器
            for line in output.split('\n'):
                if line.strip() and ',' in line:
                    parts = line.split(',')
                    if len(parts) == 10:  # 官方标准：10个字段
                        try:
                            emu_id = int(parts[0])
                            if emu_id == emulator_id:
                                # 提取运行时信息
                                top_hwnd = int(parts[2]) if parts[2] and parts[2] != '0' else -1
                                bind_hwnd = int(parts[3]) if parts[3] and parts[3] != '0' else -1
                                pid = int(parts[5]) if parts[5] and parts[5] != '0' else -1
                                return {
                                    'top_hwnd': top_hwnd,
                                    'bind_hwnd': bind_hwnd,
                                    'pid': pid
                                    # 使用雷电原生API
                                }
                        except (ValueError, IndexError):
                            continue

            return {}
        except Exception as e:
            log_error(f"获取模拟器{emulator_id}运行时信息失败: {e}")
            return {}


# ============================================================================
# 🎯 4. 全局实例管理
# ============================================================================
# 功能描述: 提供全局模拟器管理器实例的单例模式管理
# 调用关系: 被其他模块调用，获取统一的模拟器管理器实例
# 注意事项: 使用单例模式确保系统中只有一个管理器实例
# ============================================================================

# 全局实例
_emulator_manager = None

def get_emulator_manager() -> UnifiedEmulatorManager:
    """获取全局模拟器管理器实例"""
    global _emulator_manager
    if _emulator_manager is None:
        _emulator_manager = UnifiedEmulatorManager()
    return _emulator_manager
