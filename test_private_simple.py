#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试私密用户检测
"""

# 模拟从截图看到的文本
screenshot_text = "此帐户为私人帐户"

# 当前的检测关键词
private_keywords = ["私密账户", "私密账号", "私人帐户", "Private"]

print("🎯 私密用户检测测试")
print("=" * 50)
print(f"截图显示的文本: '{screenshot_text}'")
print(f"检测关键词: {private_keywords}")
print()

# 测试匹配
print("🧪 匹配测试:")
for keyword in private_keywords:
    if keyword in screenshot_text:
        print(f"  ✅ 匹配成功: '{screenshot_text}' 包含 '{keyword}'")
        break
else:
    print(f"  ❌ 匹配失败: '{screenshot_text}' 不包含任何关键词")

print()

# 分析可能的问题
print("🔍 可能的问题分析:")
print("1. 检查字符差异:")
for keyword in private_keywords:
    print(f"   关键词: '{keyword}' (长度: {len(keyword)})")
    for i, char in enumerate(keyword):
        print(f"     [{i}]: '{char}' (Unicode: {ord(char)})")

print()
print(f"   截图文本: '{screenshot_text}' (长度: {len(screenshot_text)})")
for i, char in enumerate(screenshot_text):
    print(f"     [{i}]: '{char}' (Unicode: {ord(char)})")

print()
print("2. 可能的解决方案:")
print("   - 添加更多关键词变体")
print("   - 检查XML中的实际文本内容")
print("   - 使用更宽松的匹配逻辑")
