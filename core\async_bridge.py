#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 修复版异步桥梁 - 专用事件循环管理
========================================
功能描述: 使用专用异步事件循环的UI-业务层通信桥梁
主要方法: execute_operation(), 专用事件循环管理
调用关系: 直接连接监控器到UI，减少转发层
注意事项:
- 使用专用asyncio事件循环避免Qt冲突
- 统一异步操作接口
- 线程安全的信号发送
- 删除重复的工作线程逻辑，只保留一套异步机制
========================================
"""

import asyncio
import logging
import threading
from typing import Any, List
from PyQt6.QtCore import QObject, pyqtSignal, QMetaObject, Qt, Q_ARG, pyqtSlot, QThread
from typing import Any, Optional

from .unified_emulator_manager import get_emulator_manager
from .simple_config import get_config_manager
from .logger_manager import log_info, log_error


# ============================================================================
# 🎯 1. 异步事件循环管理模块
# ============================================================================
# 功能描述: 专用异步事件循环管理，避免Qt与asyncio冲突
# 调用关系: 被FixedAsyncBridge调用，提供独立的异步执行环境
# 注意事项: 使用独立线程运行事件循环，确保线程安全
# ============================================================================

class AsyncEventLoopManager:
    """🎯 专用异步事件循环管理器 - 解决Qt与asyncio冲突"""

    def __init__(self):
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._loop_thread: Optional[threading.Thread] = None
        self._running = False

    def start_loop(self) -> asyncio.AbstractEventLoop:
        """启动专用事件循环"""
        if self._running and self._loop:
            return self._loop

        def run_loop():
            """在独立线程中运行事件循环"""
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._running = True

            try:
                self._loop.run_forever()
            except Exception as e:
                log_error(f"异步事件循环异常: {e}", component="AsyncEventLoopManager")
            finally:
                self._loop.close()
                self._running = False

        self._loop_thread = threading.Thread(target=run_loop, daemon=True, name="AsyncEventLoop")
        self._loop_thread.start()

        # 🎯 使用Qt定时器实现真正的异步等待 - 完全避免阻塞
        from PyQt6.QtCore import QTimer, QEventLoop

        loop = QEventLoop()
        timer = QTimer()
        timer.timeout.connect(loop.quit)
        timer.start(5000)  # 5秒超时

        # 使用Qt事件循环等待，完全非阻塞
        check_timer = QTimer()
        def check_running():
            if self._running:
                timer.stop()
                check_timer.stop()
                loop.quit()

        check_timer.timeout.connect(check_running)
        check_timer.start(10)  # 每10毫秒检查一次

        loop.exec()  # Qt事件循环，完全异步

        if not self._running:
            raise RuntimeError("异步事件循环启动超时")

        log_info("专用异步事件循环已启动", component="AsyncEventLoopManager")
        return self._loop

    def run_coroutine(self, coro) -> asyncio.Future:
        """在专用循环中运行协程"""
        if not self._running or not self._loop:
            self.start_loop()
        return asyncio.run_coroutine_threadsafe(coro, self._loop)

    def stop_loop(self):
        """停止事件循环"""
        if self._loop and self._running:
            self._loop.call_soon_threadsafe(self._loop.stop)
            if self._loop_thread and self._loop_thread.is_alive():
                self._loop_thread.join(timeout=5)
            self._running = False
            log_info("专用异步事件循环已停止", component="AsyncEventLoopManager")


# ============================================================================
# 🎯 2. 异步桥梁主类模块
# ============================================================================
# 功能描述: UI和业务层通信桥梁，统一异步操作入口
# 调用关系: 被UI层调用，协调各种异步操作的执行
# 注意事项: 使用专用事件循环，确保操作不阻塞UI线程
# ============================================================================

class FixedAsyncBridge(QObject):
    """🎯 修复版异步桥梁 - 使用专用事件循环"""

    # 🎯 简化信号定义 - 删除重复的状态监控信号，只保留操作信号
    operation_completed = pyqtSignal(str, object)  # 操作类型，结果
    operation_failed = pyqtSignal(str, str)        # 操作类型，错误信息
    operation_progress = pyqtSignal(str, int, int) # 操作类型，当前，总数 - 兼容性

    # 🎯 新增模拟器状态变化信号
    emulator_status_changed = pyqtSignal(int, str, str)  # 模拟器ID，旧状态，新状态

    def __init__(self):
        super().__init__()
        self.loop_manager = AsyncEventLoopManager()
        self.emulator_manager = get_emulator_manager()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 🎯 删除重复日志 - 只在main.py中输出创建日志

    # ============================================================================
    # 🎯 3. 异步操作执行模块
    # ============================================================================
    # 功能描述: 统一异步操作入口，处理各种业务操作请求
    # 调用关系: 被UI层调用，分发到具体的业务处理方法
    # 注意事项: 使用专用事件循环执行，确保线程安全
    # ============================================================================

    def execute_operation(self, operation: str, data: Any):
        """🎯 使用专用事件循环执行异步操作 - 删除重复的工作线程逻辑"""
        log_info(f"执行异步操作: {operation}", component="FixedAsyncBridge")

        # 在专用事件循环中执行异步操作，不等待结果
        self.loop_manager.run_coroutine(self._execute_async_operation(operation, data))

    async def _execute_async_operation(self, operation: str, data: Any):
        """🎯 在专用事件循环中执行的异步操作 - 统一异步逻辑"""
        try:
            if operation == 'scan_emulators':
                # 🎯 修复：使用快速扫描，不检查状态（避免阻塞）
                log_info(f"异步桥接器: 开始处理扫描请求", component="FixedAsyncBridge")

                if isinstance(data, dict):
                    emulator_path = data.get('path')
                else:
                    # 标准格式
                    emulator_path = data

                log_info(f"异步桥接器: 扫描路径 = {emulator_path}", component="FixedAsyncBridge")

                # 始终使用快速扫描，状态由监控器负责
                result = await self.emulator_manager.scan_emulators(emulator_path, False)
                log_info(f"异步桥接器: 扫描完成，结果数量 = {len(result) if result else 0}", component="FixedAsyncBridge")

                # 🎯 修复：处理不同的返回格式（字典或模型对象）
                if result and len(result) > 0:
                    # 检查第一个元素的类型
                    if hasattr(result[0], 'to_dict'):
                        # 模型对象，转换为字典
                        result = [emulator.to_dict() for emulator in result]
                        log_info(f"异步桥接器: 已转换为字典格式", component="FixedAsyncBridge")
                    # 如果已经是字典，直接使用
                else:
                    log_info(f"异步桥接器: 扫描结果为空", component="FixedAsyncBridge")
            elif operation == 'start_emulator':
                result = await self.emulator_manager.start_emulator(data)
            elif operation == 'stop_emulator':
                result = await self.emulator_manager.stop_emulator(data)
            elif operation == 'start_batch' or operation == 'batch_start':
                log_info(f"异步桥接器: 处理批量启动请求，模拟器数量: {len(data)}", component="FixedAsyncBridge")
                result = await self.emulator_manager.batch_start_emulators(data)
                log_info(f"异步桥接器: 批量启动请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")
            elif operation == 'stop_batch' or operation == 'batch_stop':
                log_info(f"异步桥接器: 处理批量停止请求，模拟器数量: {len(data)}", component="FixedAsyncBridge")
                result = await self.emulator_manager.batch_stop_emulators(data)
                log_info(f"异步桥接器: 批量停止请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")
            elif operation == 'arrange_windows':
                # 🎯 新增：手动排列窗口操作
                from core.window_arrangement_manager import get_window_arrangement_manager
                window_manager = get_window_arrangement_manager()
                window_manager.arrange_windows_manual()
                result = {'status': 'triggered', 'message': '窗口排列已触发'}
            elif operation == 'heartbeat_check':
                # 🎯 新增：心跳检测操作
                if callable(data):
                    await data()  # 执行心跳检测函数
                    result = {'status': 'completed', 'message': '心跳检测已完成'}
                else:
                    raise ValueError("心跳检测数据必须是可调用函数")
            elif operation == 'instagram_dm_task':
                # 🎯 Instagram粉丝私信任务处理
                log_info("异步桥接器: 处理Instagram粉丝私信任务请求", component="FixedAsyncBridge")
                result = await self._handle_instagram_dm_task(data)
                log_info(f"异步桥接器: Instagram粉丝私信任务请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")

            elif operation == 'instagram_follow_direct_task':
                # 🎯 Instagram直接关注任务处理
                log_info("异步桥接器: 处理Instagram直接关注任务请求", component="FixedAsyncBridge")
                result = await self._handle_instagram_follow_task(data, 'direct')
                log_info(f"异步桥接器: Instagram直接关注任务请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")

            elif operation == 'instagram_follow_fans_task':
                # 🎯 Instagram关注粉丝任务处理
                log_info("异步桥接器: 处理Instagram关注粉丝任务请求", component="FixedAsyncBridge")
                result = await self._handle_instagram_follow_task(data, 'fans')
                log_info(f"异步桥接器: Instagram关注粉丝任务请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")

            elif operation == 'close_all_tasks':
                # 🎯 新增：关闭所有任务操作
                log_info("异步桥接器: 处理关闭所有任务请求", component="FixedAsyncBridge")
                result = await self.emulator_manager.close_all_tasks()
                log_info(f"异步桥接器: 关闭所有任务请求已处理，状态: {result.get('status')}", component="FixedAsyncBridge")
                
            else:
                raise ValueError(f"未知操作: {operation}")

            # 🎯 操作成功，发送完成信号
            self._emit_operation_completed(operation, result)

            # 🎯 修复：返回操作结果
            return result

        except Exception as e:
            # 🎯 操作失败，记录错误并发送失败信号
            log_error(f"异步操作失败: {operation} - {e}", component="FixedAsyncBridge")
            self._emit_operation_failed(operation, str(e))

            # 🎯 修复：返回错误结果
            return {'status': 'failed', 'message': str(e)}

    def _emit_operation_completed(self, operation: str, result: Any):
        """🎯 线程安全地发送操作完成信号"""
        # 使用QueuedConnection确保异步操作完成信号在主线程中处理
        QMetaObject.invokeMethod(
            self, "_on_operation_completed",
            Qt.ConnectionType.QueuedConnection,
            Q_ARG(str, operation),
            Q_ARG(object, result)
        )

    def _emit_operation_failed(self, operation: str, error: str):
        """🎯 线程安全地发送操作失败信号"""
        # 使用QueuedConnection确保异步操作失败信号在主线程中处理
        QMetaObject.invokeMethod(
            self, "_on_operation_failed",
            Qt.ConnectionType.QueuedConnection,
            Q_ARG(str, operation),
            Q_ARG(str, error)
        )

    @pyqtSlot(str, object)
    def _on_operation_completed(self, operation: str, result: Any):
        """🎯 在主线程中处理操作完成"""
        # 记录操作完成日志
        log_info(f"异步操作完成: {operation}", component="FixedAsyncBridge")
        # 发送信号通知UI更新
        self.operation_completed.emit(operation, result)

    @pyqtSlot(str, str)
    def _on_operation_failed(self, operation: str, error: str):
        """🎯 在主线程中处理操作失败"""
        # 记录操作失败日志
        log_error(f"异步操作失败: {operation} - {error}", component="FixedAsyncBridge")
        # 发送信号通知UI显示错误
        self.operation_failed.emit(operation, error)



    # ============================================================================
    # 🎯 4. 资源管理模块
    # ============================================================================
    # 功能描述: 清理和生命周期管理，确保资源正确释放
    # 调用关系: 被应用程序退出时调用，清理异步资源
    # 注意事项: 停止专用事件循环，避免资源泄漏
    # ============================================================================

    def cleanup(self):
        """🎯 清理资源 - 停止专用事件循环，删除重复的清理逻辑"""
        log_info("清理修复版异步桥梁资源", component="FixedAsyncBridge")
        self.loop_manager.stop_loop()

    # ============================================================================
    # 🎯 5. 信号连接模块
    # ============================================================================
    # 功能描述: UI和业务层信号桥接，建立事件驱动通信
    # 调用关系: 被主窗口调用，连接业务层信号到UI更新
    # 注意事项: 使用事件驱动方式，避免定时轮询
    # ============================================================================

    def connect_task_signals_to_ui(self, main_window):
        """🎯 连接任务信号到UI - 事件驱动方式"""
        from core.unified_emulator_manager import get_emulator_manager
        from core.window_arrangement_manager import get_window_arrangement_manager

        emulator_manager = get_emulator_manager()
        window_manager = get_window_arrangement_manager()

        # 🎯 连接批量操作信号 - 恢复扫描功能
        main_window.batch_operation_requested.connect(self.execute_operation)

        # 🎯 连接任务完成信号
        emulator_manager.task_finished.connect(main_window.on_task_finished)

        # 🎯 连接任务完成信号到窗口排列管理器 - 新增
        emulator_manager.task_finished.connect(window_manager.on_emulator_startup_completed)
        print(f"[FixedAsyncBridge] 任务完成信号已连接到窗口排列管理器")
        log_info("任务完成信号已连接到窗口排列管理器", component="FixedAsyncBridge")

        # 🎯 连接启动管理器的状态变化信号
        emulator_manager.startup_manager.emulator_state_changed.connect(main_window.on_emulator_status_changed)

        # 🎯 连接批量状态变化信号 - 避免重复日志
        emulator_manager.startup_manager.batch_state_changed.connect(main_window.on_batch_status_changed)

        # 🎯 连接详细状态变化信号
        if hasattr(emulator_manager, 'emulator_state_changed_detailed'):
            emulator_manager.emulator_state_changed_detailed.connect(main_window.on_emulator_status_changed_detailed)

        # 🎯 连接启动进度信号
        emulator_manager.startup_manager.startup_progress.connect(main_window.on_startup_progress_updated)

        # 🎯 连接心跳管理器信号到UI - 新增
        heartbeat_manager = emulator_manager.heartbeat_manager
        if hasattr(main_window, 'on_heartbeat_success'):
            heartbeat_manager.heartbeat_success.connect(main_window.on_heartbeat_success)
        if hasattr(main_window, 'on_heartbeat_failed'):
            heartbeat_manager.heartbeat_failed.connect(main_window.on_heartbeat_failed)
        if hasattr(main_window, 'on_emulator_recovered'):
            heartbeat_manager.emulator_recovered.connect(main_window.on_emulator_recovered)
        if hasattr(main_window, 'on_emulator_switched'):
            heartbeat_manager.emulator_switched.connect(main_window.on_emulator_switched)

        # 🎯 连接窗口排列管理器信号到UI - 新增
        window_manager.arrangement_started.connect(main_window.on_arrangement_started)
        window_manager.arrangement_completed.connect(main_window.on_arrangement_completed)
        window_manager.arrangement_failed.connect(main_window.on_arrangement_failed)

        log_info("任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI", component="FixedAsyncBridge")

    def connect_monitor_to_ui(self, main_window):
        """🎯 删除监控器连接 - 采用参考代码的事件驱动方式"""
        # 🎯 参考代码不使用定时监控器，改为事件驱动
        # 任务完成时直接通过信号更新UI
        log_info("采用事件驱动方式，无需监控器", component="FixedAsyncBridge")

    def start_background_services(self):
        """启动后台服务 - 参考代码的简化方式"""
        from core.config_hot_reload import get_config_hot_reload_service

        # 🎯 删除监控器 - 采用参考代码的事件驱动方式，无需定时监控
        # 启动配置热加载服务
        hot_reload_service = get_config_hot_reload_service()
        hot_reload_service.start()

        log_info("后台服务已启动", component="FixedAsyncBridge")

    async def _handle_instagram_dm_task(self, data: dict):
        """
        处理Instagram粉丝私信任务 - 线程池模式

        执行步骤：
        1. 解析任务参数，获取模拟器ID列表
        2. 批量启动模拟器（排队 → 启动中 → 运行中）
        3. 创建Instagram任务管理器，管理线程池和并发
        4. 为每个模拟器创建Instagram任务线程
        5. 启动并发任务执行（等待启动 → 窗口排列 → Instagram任务）
        """
        try:
            # 🎯 步骤1：解析任务参数
            emulator_ids = data.get('emulator_ids', [])

            # 兼容旧格式
            if not emulator_ids:
                emulators = data.get('emulators', [])
                emulator_ids = [emulator['id'] for emulator in emulators]

            if not emulator_ids:
                return {
                    'status': 'failed',
                    'message': '未指定有效的模拟器ID',
                    'emulator_count': 0
                }

            log_info(f"开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: {len(emulator_ids)}", component="FixedAsyncBridge")

            # 🎯 步骤2：批量启动模拟器（使用"启动选中"功能）
            log_info(f"使用批量启动功能启动模拟器: {emulator_ids}", component="FixedAsyncBridge")
            startup_result = await self._execute_async_operation('start_batch', emulator_ids)

            if startup_result and startup_result.get('status') == 'queued':
                log_info(f"模拟器批量启动成功，开始创建任务线程池", component="FixedAsyncBridge")

                # 🎯 步骤3：创建Instagram任务管理器（管理线程池和并发）
                task_manager = InstagramTaskManager(self.emulator_manager, self)

                # 🎯 保存任务管理器引用，防止被垃圾回收
                if not hasattr(self, 'active_task_managers'):
                    self.active_task_managers = {}
                task_id = f'instagram_dm_{len(self.active_task_managers)}'
                self.active_task_managers[task_id] = task_manager

                # 🎯 连接信号到UI
                task_manager.all_tasks_completed.connect(self._on_instagram_tasks_completed)
                task_manager.task_progress_updated.connect(self._on_instagram_task_progress)

                # 🎯 步骤4：启动并发任务执行（等待启动 → 窗口排列 → Instagram任务）
                task_manager.start_concurrent_tasks(emulator_ids)
            else:
                log_error(f"模拟器批量启动失败: {startup_result}", component="FixedAsyncBridge")
                return {
                    'status': 'failed',
                    'message': f'模拟器启动失败: {startup_result.get("message", "未知错误") if startup_result else "启动结果为空"}',
                    'emulator_count': 0
                }

            return {
                'status': 'started',
                'message': f'Instagram粉丝私信任务已启动，涉及{len(emulator_ids)}个模拟器（线程池并发模式）',
                'emulator_count': len(emulator_ids)
            }

        except Exception as e:
            log_error(f"处理Instagram粉丝私信任务失败: {e}", component="FixedAsyncBridge")
            return {
                'status': 'failed',
                'message': f'Instagram粉丝私信任务启动失败: {str(e)}',
                'emulator_count': 0
            }

    async def _handle_instagram_follow_task(self, data: dict, follow_mode: str):
        """
        处理Instagram关注任务 - 线程池模式

        执行步骤：
        1. 解析任务参数，获取模拟器ID列表
        2. 批量启动模拟器（排队 → 启动中 → 运行中）
        3. 创建Instagram关注任务管理器，管理线程池和并发
        4. 为每个模拟器创建Instagram关注任务线程
        5. 启动并发任务执行（等待启动 → 窗口排列 → Instagram关注任务）

        参数:
        - data: 任务数据，包含模拟器ID列表
        - follow_mode: 关注模式，'direct'=直接关注, 'fans'=关注粉丝
        """
        try:
            # 🎯 步骤1：解析任务参数
            emulator_ids = data.get('emulator_ids', [])

            # 兼容旧格式
            if not emulator_ids:
                emulators = data.get('emulators', [])
                emulator_ids = [emulator['id'] for emulator in emulators]

            if not emulator_ids:
                return {
                    'status': 'failed',
                    'message': '未指定有效的模拟器ID',
                    'emulator_count': 0
                }

            task_name = "Instagram直接关注" if follow_mode == 'direct' else "Instagram关注粉丝"
            log_info(f"开始处理{task_name}任务（线程池模式），模拟器数量: {len(emulator_ids)}", component="FixedAsyncBridge")

            # 🎯 步骤2：批量启动模拟器（使用"启动选中"功能）
            log_info(f"使用批量启动功能启动模拟器: {emulator_ids}", component="FixedAsyncBridge")
            startup_result = await self._execute_async_operation('start_batch', emulator_ids)

            if startup_result and startup_result.get('status') == 'queued':
                log_info(f"模拟器批量启动成功，开始创建关注任务线程池", component="FixedAsyncBridge")

                # 🎯 步骤3：创建Instagram关注任务管理器（管理线程池和并发）
                task_manager = InstagramFollowTaskManager(self.emulator_manager, self, follow_mode)

                # 🎯 保存任务管理器引用，防止被垃圾回收
                if not hasattr(self, 'active_task_managers'):
                    self.active_task_managers = {}
                task_id = f'instagram_follow_{follow_mode}_{len(self.active_task_managers)}'
                self.active_task_managers[task_id] = task_manager

                # 🎯 连接信号到UI
                task_manager.all_tasks_completed.connect(self._on_instagram_tasks_completed)
                task_manager.task_progress_updated.connect(self._on_instagram_task_progress)

                # 🎯 步骤4：启动并发任务执行（等待启动 → 窗口排列 → Instagram关注任务）
                task_manager.start_concurrent_tasks(emulator_ids)
            else:
                log_error(f"模拟器批量启动失败: {startup_result}", component="FixedAsyncBridge")
                return {
                    'status': 'failed',
                    'message': f'模拟器启动失败: {startup_result.get("message", "未知错误") if startup_result else "启动结果为空"}',
                    'emulator_count': 0
                }

            return {
                'status': 'started',
                'message': f'{task_name}任务已启动，涉及{len(emulator_ids)}个模拟器（线程池并发模式）',
                'emulator_count': len(emulator_ids)
            }

        except Exception as e:
            task_name = "Instagram直接关注" if follow_mode == 'direct' else "Instagram关注粉丝"
            log_error(f"处理{task_name}任务失败: {e}", component="FixedAsyncBridge")
            return {
                'status': 'failed',
                'message': f'{task_name}任务启动失败: {str(e)}',
                'emulator_count': 0
            }

    def _on_instagram_tasks_completed(self, results):
        """处理所有Instagram任务完成"""
        try:
            completed_count = len([r for r in results if len(r) == 2 and isinstance(r[1], dict)])
            failed_count = len([r for r in results if len(r) == 2 and isinstance(r[1], str)])

            log_info(f"所有Instagram任务执行完成: 成功{completed_count}个, 失败{failed_count}个",
                    component="FixedAsyncBridge")

            # 🎯 清理已完成的任务管理器
            if hasattr(self, 'active_task_managers'):
                # 找到发送信号的任务管理器并清理
                sender = self.sender()
                for task_id, manager in list(self.active_task_managers.items()):
                    if manager == sender:
                        del self.active_task_managers[task_id]
                        log_info(f"清理已完成的任务管理器: {task_id}", component="FixedAsyncBridge")
                        break

        except Exception as e:
            log_error(f"处理Instagram任务完成异常: {str(e)}", component="FixedAsyncBridge")

    def _on_instagram_task_progress(self, emulator_id: int, stage: str, message: str):
        """处理Instagram任务进度更新"""
        log_info(f"[模拟器{emulator_id}] {stage}: {message}", component="FixedAsyncBridge")


# ============================================================================
# 🎯 Instagram任务线程类 - 集成启动逻辑的独立线程
# ============================================================================

class InstagramTaskThread(QThread):
    """Instagram任务线程 - 处理单个模拟器的完整流程"""

    # 信号定义
    task_started = pyqtSignal(int, str)  # emulator_id, task_name
    task_progress = pyqtSignal(int, str, str)  # emulator_id, stage, message
    task_completed = pyqtSignal(int, str, dict)  # emulator_id, task_name, result
    task_failed = pyqtSignal(int, str, str)  # emulator_id, task_name, error

    def __init__(self, emulator_id: int, task_config: dict):
        super().__init__()
        self.emulator_id = emulator_id
        self.task_config = task_config
        self.emulator_manager = get_emulator_manager()
        self.config_manager = get_config_manager()
        self.should_stop = False

    def run(self):
        """线程主执行方法 - 等待启动完成并执行任务"""
        try:
            # 🎯 记录任务开始时间（参考代码实现：从线程启动就开始计时）
            import time
            self.task_start_time = time.time()

            self.task_started.emit(self.emulator_id, "instagram_dm")

            # 🎯 第1步：等待启动完成（模拟器已经通过批量启动功能启动）
            self.task_progress.emit(self.emulator_id, "waiting", "等待模拟器启动完成")
            if not self._wait_for_startup_completion():
                return

            # 🎯 第2步：窗口排列
            self.task_progress.emit(self.emulator_id, "arrangement", "等待窗口排列完成")
            if not self._arrange_window():
                return

            # 🎯 第3步：执行Instagram任务
            self.task_progress.emit(self.emulator_id, "instagram", "执行Instagram私信任务")
            self._execute_instagram_task()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 线程执行异常: {str(e)}", component="InstagramTaskThread")
            self.task_failed.emit(self.emulator_id, "instagram_dm", str(e))



    def _wait_for_startup_completion(self) -> bool:
        """智能等待模拟器启动完成 - 遵循现有架构约束，及时响应StartupManager状态"""
        import time

        log_info(f"[模拟器{self.emulator_id}] 开始等待启动完成", component="InstagramTaskThread")

        # 🎯 使用现有配置的等待时间，作为兜底超时保护
        max_wait_time = 300  # 5分钟兜底超时
        start_time = time.time()
        check_interval = 2   # 每2秒检查一次状态

        while not self.should_stop and (time.time() - start_time < max_wait_time):
            try:
                # 🎯 关键修改1：获取StartupManager的实时状态
                startup_manager = self.emulator_manager.startup_manager
                emulator_state = startup_manager.emulator_states.get(self.emulator_id, "未知")

                # 🎯 关键修改2：如果StartupManager已经最终失败，立即退出
                from core.status_converter import EmulatorStatus
                if emulator_state == EmulatorStatus.FAILED:
                    log_error(f"[模拟器{self.emulator_id}] ❌ StartupManager已确认启动失败，任务线程立即退出",
                             component="InstagramTaskThread")
                    self.task_failed.emit(self.emulator_id, "instagram_dm", f"启动失败: {emulator_state}")
                    return False

                # 🎯 关键修改3：如果StartupManager已经取消，立即退出
                if emulator_state == EmulatorStatus.CANCELLED:
                    log_error(f"[模拟器{self.emulator_id}] ❌ StartupManager已取消启动，任务线程立即退出",
                             component="InstagramTaskThread")
                    self.task_failed.emit(self.emulator_id, "instagram_dm", f"启动已取消: {emulator_state}")
                    return False

                # 🎯 关键修改4：如果模拟器已经启动成功，立即返回
                if self._check_emulator_running():
                    elapsed = time.time() - start_time
                    log_info(f"[模拟器{self.emulator_id}] ✅ 模拟器启动完成，等待时间: {elapsed:.1f}秒",
                            component="InstagramTaskThread")
                    return True

                # 🎯 关键修改5：检查是否还在active_startups中（正在重试）
                if self.emulator_id not in startup_manager.active_startups:
                    # 不在active_startups中，说明StartupManager已经处理完毕
                    if emulator_state not in [EmulatorStatus.RUNNING, EmulatorStatus.STARTING]:
                        log_error(f"[模拟器{self.emulator_id}] ❌ StartupManager已完成处理但未成功，状态: {emulator_state}",
                                 component="InstagramTaskThread")
                        self.task_failed.emit(self.emulator_id, "instagram_dm", f"启动处理完毕但失败: {emulator_state}")
                        return False

                # 🎯 继续等待，但输出当前状态
                elapsed = time.time() - start_time
                log_info(f"[模拟器{self.emulator_id}] 等待启动中... 状态: {emulator_state}, 已等待: {elapsed:.1f}秒",
                        component="InstagramTaskThread")

                time.sleep(check_interval)

            except Exception as e:
                log_error(f"[模拟器{self.emulator_id}] 启动状态检查异常: {e}", component="InstagramTaskThread")
                time.sleep(check_interval)

        # 🎯 兜底超时处理
        log_error(f"[模拟器{self.emulator_id}] ❌ 等待启动完成超时({max_wait_time}秒)", component="InstagramTaskThread")
        self.task_failed.emit(self.emulator_id, "instagram_dm", "等待启动完成超时")
        return False

    def _check_emulator_running(self) -> bool:
        """检查模拟器运行状态"""
        try:
            return (self.emulator_id in self.emulator_manager.startup_manager.running_emulators or
                    self.emulator_manager.startup_manager.emulator_states.get(self.emulator_id) == "运行中")
        except:
            return False

    def _arrange_window(self) -> bool:
        """窗口排列"""
        try:
            import time

            log_info(f"[模拟器{self.emulator_id}] 开始窗口排列", component="InstagramTaskThread")

            # 简单等待窗口排列完成
            arrangement_delay = self.config_manager.get('window.arrangement_delay', 8)
            time.sleep(arrangement_delay)

            log_info(f"[模拟器{self.emulator_id}] 窗口排列完成", component="InstagramTaskThread")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 窗口排列异常: {str(e)}", component="InstagramTaskThread")
            self.task_failed.emit(self.emulator_id, "instagram_dm", f"窗口排列异常: {str(e)}")
            return False

    def _execute_instagram_task(self):
        """执行Instagram任务"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行Instagram私信任务", component="InstagramTaskThread")

            # 创建Instagram任务实例
            from .instagram_task import InstagramDMTask
            task = InstagramDMTask(self.emulator_id)

            # 🎯 传递任务开始时间（从线程启动时就开始计时）
            if hasattr(self, 'task_start_time'):
                task.task_start_time = self.task_start_time
                log_info(f"[模拟器{self.emulator_id}] 任务超时计时已从线程启动开始: {task.task_timeout}秒", component="InstagramTaskThread")

            # 执行任务（同步执行）
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(task.execute())

                # 处理任务结果
                if result.get('status') == 'completed':
                    log_info(f"[模拟器{self.emulator_id}] Instagram任务执行成功: {result.get('message')}", component="InstagramTaskThread")
                    self.task_completed.emit(self.emulator_id, 'instagram_dm', result)
                else:
                    log_error(f"[模拟器{self.emulator_id}] Instagram任务执行失败: {result.get('message')}", component="InstagramTaskThread")
                    self.task_failed.emit(self.emulator_id, 'instagram_dm', result.get('message', '未知错误'))
            finally:
                loop.close()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram任务执行异常: {str(e)}", component="InstagramTaskThread")
            self.task_failed.emit(self.emulator_id, 'instagram_dm', str(e))

    def stop(self):
        """停止线程"""
        self.should_stop = True


# ============================================================================
# 🎯 Instagram任务管理器 - 管理线程池和并发控制
# ============================================================================

class InstagramTaskManager(QObject):
    """Instagram任务管理器 - 管理多个任务线程的并发执行"""

    # 信号定义
    all_tasks_completed = pyqtSignal(list)  # 所有任务完成
    task_progress_updated = pyqtSignal(int, str, str)  # 任务进度更新

    def __init__(self, emulator_manager, async_bridge):
        super().__init__()
        self.emulator_manager = emulator_manager
        self.async_bridge = async_bridge

        # 线程池管理
        self.thread_pool = {}  # emulator_id -> InstagramTaskThread
        self.pending_tasks = []  # 待执行任务队列
        self.running_threads = {}  # 正在运行的线程
        self.completed_threads = []  # 已完成的线程
        self.failed_threads = []  # 失败的线程

        # 并发控制
        self.max_concurrent = self._get_max_concurrent()
        self.current_running = 0

        # 🎯 修复：初始化task_config属性
        self.task_config = self._get_task_config()

        # 🎯 关键修复：连接模拟器状态变化信号，处理心跳异常
        if hasattr(emulator_manager, 'startup_manager'):
            emulator_manager.startup_manager.emulator_state_changed.connect(self._on_emulator_state_changed)
            log_info(f"Instagram任务管理器已连接模拟器状态变化信号", component="InstagramTaskManager")

        log_info(f"Instagram任务管理器初始化完成，最大并发: {self.max_concurrent}", component="InstagramTaskManager")

    def _get_max_concurrent(self) -> int:
        """获取最大并发数 - 使用系统设置的现有参数"""
        config_manager = get_config_manager()
        # 🎯 直接使用系统设置中的"最大并发任务数"
        return config_manager.get('max_concurrent_tasks', 2)

    def _get_task_config(self) -> dict:
        """获取任务配置 - 使用系统设置的现有参数"""
        config_manager = get_config_manager()
        return {
            'startup_timeout': config_manager.get('emulator.startup_timeout', 25),  # 系统设置：模拟器启动超时
            'startup_retry_count': config_manager.get('emulator.startup_retry_count', 3),  # 系统设置：启动超时重试次数
            'startup_interval': config_manager.get('emulator.startup_interval', 3),  # 系统设置：模拟器启动间隔
            'task_relay_interval': config_manager.get('emulator.task_relay_interval', 2),  # 系统设置：任务接力间隔
            'window_arrangement_delay': config_manager.get('window.arrangement_delay', 8)
        }

    def start_concurrent_tasks(self, emulator_ids: List[int]):
        """
        启动并发Instagram任务

        执行步骤：
        1. 为每个模拟器创建Instagram任务线程并加入线程池
        2. 开始调度任务执行（根据并发限制启动线程）
        """
        try:
            log_info(f"开始启动Instagram任务管理器，模拟器数量: {len(emulator_ids)}", component="InstagramTaskManager")

            # 🎯 步骤1：为每个模拟器创建Instagram任务线程
            created_count = 0
            failed_count = 0

            for emulator_id in emulator_ids:
                success = self._create_instagram_thread_for_emulator(emulator_id, add_to_pool=True)
                if success:
                    created_count += 1
                else:
                    failed_count += 1

            # 统一打印创建结果
            if failed_count > 0:
                log_info(f"线程池创建完成: 成功{created_count}个, 失败{failed_count}个, 总计{len(emulator_ids)}个", component="InstagramTaskManager")
            else:
                log_info(f"线程池创建完成: 成功创建{created_count}个Instagram线程", component="InstagramTaskManager")

            # 🎯 步骤2：开始调度任务执行（根据并发限制）
            self._schedule_next_tasks()

        except Exception as e:
            log_error(f"启动并发任务失败: {str(e)}", component="InstagramTaskManager")

    def _schedule_next_tasks(self):
        """调度下一批任务"""
        try:
            # 控制并发数量，启动下一个任务
            while (self.current_running < self.max_concurrent and
                   len(self.thread_pool) > 0):

                emulator_id, thread = next(iter(self.thread_pool.items()))
                del self.thread_pool[emulator_id]
                self.running_threads[emulator_id] = thread
                self.current_running += 1

                log_info(f"启动模拟器{emulator_id}的Instagram任务线程 - 当前并发: {self.current_running}/{self.max_concurrent}", component="InstagramTaskManager")
                thread.start()

        except Exception as e:
            log_error(f"调度任务异常: {str(e)}", component="InstagramTaskManager")

    def _on_task_completed(self, emulator_id: int, task_name: str, result: dict):
        """处理任务完成"""
        try:
            log_info(f"模拟器{emulator_id}的{task_name}任务完成", component="InstagramTaskManager")

            # 移动到完成列表
            if emulator_id in self.running_threads:
                self.running_threads.pop(emulator_id)
                self.completed_threads.append((emulator_id, result))
                self.current_running -= 1

            # 🎯 统一接力：检查排队模拟器（包含调度逻辑）
            self._check_and_start_queued_instagram_tasks()

            # 调度线程池中的下一个任务
            self._schedule_next_tasks()

            # 检查是否所有任务完成
            self._check_all_tasks_completed()

        except Exception as e:
            log_error(f"处理任务完成异常: {str(e)}", component="InstagramTaskManager")

    def _on_task_failed(self, emulator_id: int, task_name: str, error: str):
        """处理任务失败"""
        try:
            log_error(f"模拟器{emulator_id}的{task_name}任务失败: {error}", component="InstagramTaskManager")

            # 移动到失败列表
            if emulator_id in self.running_threads:
                self.running_threads.pop(emulator_id)
                self.failed_threads.append((emulator_id, error))
                self.current_running -= 1

            # 🎯 统一接力：检查排队模拟器（包含调度逻辑）
            self._check_and_start_queued_instagram_tasks()

            # 调度线程池中的下一个任务
            self._schedule_next_tasks()

            # 检查是否所有任务完成
            self._check_all_tasks_completed()

        except Exception as e:
            log_error(f"处理任务失败异常: {str(e)}", component="InstagramTaskManager")

    def _on_task_progress(self, emulator_id: int, stage: str, message: str):
        """处理任务进度更新"""
        log_info(f"模拟器{emulator_id} - {stage}: {message}", component="InstagramTaskManager")
        self.task_progress_updated.emit(emulator_id, stage, message)

    def _check_all_tasks_completed(self):
        """检查是否所有任务都完成了"""
        if (len(self.thread_pool) == 0 and
            len(self.running_threads) == 0):

            total_completed = len(self.completed_threads)
            total_failed = len(self.failed_threads)
            total_tasks = total_completed + total_failed

            log_info(f"所有Instagram任务执行完成: 成功{total_completed}个, 失败{total_failed}个, 总计{total_tasks}个",
                    component="InstagramTaskManager")

            # 发送完成信号
            self.all_tasks_completed.emit(self.completed_threads + self.failed_threads)

    def _check_and_start_queued_instagram_tasks(self):
        """
        简化的任务接力逻辑：有排队就启动

        执行步骤：
        1. 检查是否有排队等待的模拟器
        2. 为下一个排队模拟器创建Instagram任务线程
        3. 触发任务调度执行
        """
        try:
            # 🎯 步骤1：检查是否有排队的模拟器
            if hasattr(self.emulator_manager, 'startup_manager'):
                startup_manager = self.emulator_manager.startup_manager
                if hasattr(startup_manager, 'startup_queue') and startup_manager.startup_queue:
                    # 🎯 步骤2：为下一个排队模拟器创建Instagram线程
                    next_emulator = startup_manager.startup_queue[0]
                    log_info(f"为接力排队模拟器{next_emulator}创建Instagram任务线程", component="InstagramTaskManager")
                    self._create_instagram_thread_for_emulator(next_emulator)

        except Exception as e:
            log_error(f"检查排队Instagram任务失败: {str(e)}", component="InstagramTaskManager")

    def _on_emulator_state_changed(self, emulator_id: int, old_state: str, new_state: str):
        """
        处理模拟器状态变化 - 心跳异常时的任务接力关键逻辑

        执行步骤：
        1. 监听模拟器状态变化（启动中 → 运行中 或 运行中 → 异常/失败/停止等）
        2. 当模拟器启动成功时，启动对应的Instagram任务线程
        3. 当模拟器状态变为异常时，清理对应的Instagram线程，释放并发槽位
        4. 检查并启动排队模拟器的Instagram任务（任务接力）
        5. 调度线程池中的下一个任务
        """
        try:
            from core.status_converter import EmulatorStatus

            log_info(f"Instagram任务管理器收到模拟器{emulator_id}状态变化: {old_state} -> {new_state}", component="InstagramTaskManager")

            # 🎯 步骤1-2：当模拟器启动成功时，启动对应的Instagram任务线程
            if new_state == EmulatorStatus.RUNNING and old_state == EmulatorStatus.STARTING:
                # 检查该模拟器是否有等待中的Instagram任务线程
                if emulator_id in self.thread_pool:
                    thread = self.thread_pool.pop(emulator_id)
                    self.running_threads[emulator_id] = thread
                    self.current_running += 1
                    log_info(f"启动模拟器{emulator_id}的Instagram私信任务线程 - 当前并发: {self.current_running}/{self.max_concurrent}", component="InstagramTaskManager")
                    thread.start()

            # 🎯 步骤3-5：当模拟器状态变为异常时，清理对应的Instagram线程
            elif new_state in [EmulatorStatus.ABNORMAL, EmulatorStatus.FAILED, EmulatorStatus.STOPPED, EmulatorStatus.CANCELLED]:
                self._cleanup_emulator_thread(emulator_id, f"模拟器状态变为{new_state}")

                # 🎯 步骤4：检查并启动排队模拟器的Instagram任务（任务接力）
                self._check_and_start_queued_instagram_tasks()

                # 🎯 步骤5：调度线程池中的下一个任务
                self._schedule_next_tasks()

        except Exception as e:
            log_error(f"处理模拟器状态变化异常: {str(e)}", component="InstagramTaskManager")

    def _cleanup_emulator_thread(self, emulator_id: int, reason: str):
        """
        清理指定模拟器的Instagram线程

        执行步骤：
        1. 清理运行中的线程，释放并发槽位
        2. 清理线程池中的线程
        3. 停止线程执行，释放资源
        """
        try:
            cleaned = False

            # 🎯 步骤1：清理运行中的线程，释放并发槽位
            if emulator_id in self.running_threads:
                thread = self.running_threads.pop(emulator_id)
                thread.stop()  # 停止线程
                self.current_running -= 1
                cleaned = True
                log_info(f"清理模拟器{emulator_id}的运行中Instagram线程，原因: {reason}", component="InstagramTaskManager")

            # 🎯 步骤2：清理线程池中的线程
            if emulator_id in self.thread_pool:
                thread = self.thread_pool.pop(emulator_id)
                thread.stop()  # 停止线程
                cleaned = True
                log_info(f"清理模拟器{emulator_id}的线程池Instagram线程，原因: {reason}", component="InstagramTaskManager")

            if cleaned:
                log_info(f"模拟器{emulator_id}的Instagram线程已清理完成，当前并发: {self.current_running}/{self.max_concurrent}", component="InstagramTaskManager")
            else:
                log_info(f"模拟器{emulator_id}没有需要清理的Instagram线程", component="InstagramTaskManager")

        except Exception as e:
            log_error(f"清理模拟器{emulator_id}线程失败: {str(e)}", component="InstagramTaskManager")



    def _create_instagram_thread_for_emulator(self, emulator_id: int, add_to_pool: bool = False) -> bool:
        """为单个模拟器创建Instagram线程 - 统一线程创建逻辑"""
        try:
            # 创建新的Instagram线程
            thread = InstagramTaskThread(emulator_id, self.task_config)

            # 连接信号
            thread.task_completed.connect(self._on_task_completed)
            thread.task_failed.connect(self._on_task_failed)
            thread.task_progress.connect(self._on_task_progress)

            if add_to_pool:
                # 🎯 初始创建：添加到线程池等待调度
                self.thread_pool[emulator_id] = thread
                # 移除单独的日志，改为批量统计
                # log_info(f"为模拟器{emulator_id}创建Instagram线程并添加到线程池", component="InstagramTaskManager")
            else:
                # 🎯 接力创建：立即启动线程（模拟器会在排队阶段等待）
                self.running_threads[emulator_id] = thread
                self.current_running += 1

                log_info(f"为接力模拟器{emulator_id}创建并启动Instagram任务线程 - 当前并发: {self.current_running}/{self.max_concurrent}",
                        component="InstagramTaskManager")
                thread.start()

            return True

        except Exception as e:
            log_error(f"为模拟器{emulator_id}创建Instagram线程失败: {str(e)}", component="InstagramTaskManager")
            return False

    def stop_all_tasks(self):
        """停止所有任务"""
        try:
            log_info("停止所有Instagram任务", component="InstagramTaskManager")

            # 停止所有运行中的线程
            for thread in self.running_threads.values():
                thread.stop()
                thread.wait(5000)  # 等待最多5秒

            # 停止所有等待中的线程
            for thread in self.thread_pool.values():
                thread.stop()
                thread.wait(1000)  # 等待最多1秒

        except Exception as e:
            log_error(f"停止任务异常: {str(e)}", component="InstagramTaskManager")

    def __del__(self):
        """析构函数 - 确保线程正确清理"""
        try:
            self.stop_all_tasks()
        except:
            pass

    def shutdown(self):
        """🎯 关闭异步桥梁 - 统一清理所有服务"""
        log_info("关闭异步桥梁", component="FixedAsyncBridge")

        # 停止后台服务
        try:
            from core.config_hot_reload import get_config_hot_reload_service

            # 停止配置热加载服务
            hot_reload_service = get_config_hot_reload_service()
            hot_reload_service.stop()

            log_info("后台服务已停止", component="FixedAsyncBridge")
        except Exception as e:
            log_error(f"停止后台服务失败: {e}", component="FixedAsyncBridge")

        # 清理资源
        self.cleanup()


# ============================================================================
# 🎯 Instagram关注任务管理器 - 管理关注任务线程池和并发控制
# ============================================================================

class InstagramFollowTaskManager(InstagramTaskManager):
    """Instagram关注任务管理器 - 继承私信任务管理器的完整架构"""

    def __init__(self, emulator_manager, async_bridge, follow_mode: str):
        """
        初始化Instagram关注任务管理器
        ========================================
        功能描述: 继承InstagramTaskManager的完整架构，添加关注模式支持
        参数:
        - emulator_manager: 模拟器管理器
        - async_bridge: 异步桥梁
        - follow_mode: 关注模式，'direct'=直接关注, 'fans'=关注粉丝
        ========================================
        """
        # ✅ 完全继承父类初始化，自动获得所有统一设置
        super().__init__(emulator_manager, async_bridge)

        # 🎯 关注任务特有的配置
        self.follow_mode = follow_mode

        log_info(f"Instagram关注任务管理器初始化完成，模式: {follow_mode}", component="InstagramFollowTaskManager")

    def _on_emulator_state_changed(self, emulator_id: int, old_state: str, new_state: str):
        """
        重写父类方法：处理模拟器状态变化 - 专门为关注任务添加启动成功处理
        """
        try:
            from core.status_converter import EmulatorStatus

            log_info(f"Instagram关注任务管理器收到模拟器{emulator_id}状态变化: {old_state} -> {new_state}", component="InstagramFollowTaskManager")

            # 🎯 关注任务专用：当模拟器启动成功时，启动对应的Instagram任务线程
            if new_state == EmulatorStatus.RUNNING and old_state == EmulatorStatus.STARTING:
                # 检查该模拟器是否有等待中的Instagram任务线程
                if emulator_id in self.thread_pool:
                    thread = self.thread_pool.pop(emulator_id)
                    self.running_threads[emulator_id] = thread
                    self.current_running += 1
                    log_info(f"启动模拟器{emulator_id}的Instagram关注任务线程 - 当前并发: {self.current_running}/{self.max_concurrent}", component="InstagramFollowTaskManager")
                    thread.start()

            # 🎯 调用父类方法处理其他状态变化
            else:
                super()._on_emulator_state_changed(emulator_id, old_state, new_state)

        except Exception as e:
            log_error(f"Instagram关注任务管理器处理模拟器状态变化异常: {str(e)}", component="InstagramFollowTaskManager")

    def _on_task_started(self, emulator_id: int, task_name: str):
        """处理任务开始"""
        log_info(f"模拟器{emulator_id}的{task_name}任务已开始", component="InstagramFollowTaskManager")

    def _create_instagram_thread_for_emulator(self, emulator_id: int, add_to_pool: bool = False) -> bool:
        """
        为模拟器创建Instagram关注任务线程
        ========================================
        功能描述: 重写父类方法，创建关注任务线程而不是私信任务线程
        参数:
        - emulator_id: 模拟器ID
        - add_to_pool: 是否添加到线程池
        返回值: bool - 创建是否成功
        ========================================
        """
        try:
            # 🎯 创建关注任务线程（而不是私信任务线程）
            thread = InstagramFollowTaskThread(emulator_id, self.task_config, self.follow_mode)

            # 🎯 连接线程信号（复用父类的信号处理逻辑）
            thread.task_started.connect(self._on_task_started)
            thread.task_progress.connect(self._on_task_progress)
            thread.task_completed.connect(self._on_task_completed)
            thread.task_failed.connect(self._on_task_failed)

            if add_to_pool:
                # 🎯 初始创建：添加到线程池等待调度
                self.thread_pool[emulator_id] = thread
            else:
                # 🎯 接力创建：立即启动线程（模拟器会在排队阶段等待）
                self.running_threads[emulator_id] = thread
                self.current_running += 1

                log_info(f"为接力模拟器{emulator_id}创建并启动Instagram关注任务线程 - 当前并发: {self.current_running}/{self.max_concurrent}",
                        component="InstagramFollowTaskManager")
                thread.start()

            log_info(f"为模拟器 {emulator_id} 创建Instagram关注任务线程，模式: {self.follow_mode}",
                    component="InstagramFollowTaskManager")

            return True

        except Exception as e:
            log_error(f"为模拟器 {emulator_id} 创建关注任务线程失败: {e}", component="InstagramFollowTaskManager")
            return False


# ============================================================================
# 🎯 Instagram关注任务线程类 - 集成启动逻辑的独立线程
# ============================================================================

class InstagramFollowTaskThread(InstagramTaskThread):
    """Instagram关注任务线程 - 继承私信任务线程的完整架构"""

    def __init__(self, emulator_id: int, task_config: dict, follow_mode: str):
        """
        初始化Instagram关注任务线程
        ========================================
        功能描述: 继承InstagramTaskThread的完整架构，添加关注模式支持
        参数:
        - emulator_id: 模拟器ID
        - task_config: 任务配置
        - follow_mode: 关注模式，'direct'=直接关注, 'fans'=关注粉丝
        ========================================
        """
        # ✅ 完全继承父类初始化，自动获得所有统一设置
        super().__init__(emulator_id, task_config)

        # 🎯 关注任务特有的配置
        self.follow_mode = follow_mode

        log_info(f"[模拟器{emulator_id}] Instagram关注任务线程初始化完成，模式: {follow_mode}",
                component="InstagramFollowTaskThread")

    def run(self):
        """线程主执行方法 - 复用父类流程，替换任务执行部分"""
        try:
            task_name = f"instagram_follow_{self.follow_mode}"
            self.task_started.emit(self.emulator_id, task_name)

            # ✅ 第1步：等待启动完成（完全继承父类）
            self.task_progress.emit(self.emulator_id, "waiting", "等待模拟器启动完成")
            if not self._wait_for_startup_completion():
                return

            # ✅ 第2步：窗口排列（完全继承父类）
            self.task_progress.emit(self.emulator_id, "arrangement", "等待窗口排列完成")
            if not self._arrange_window():
                return

            # 🆕 第3步：执行Instagram关注任务（替换私信任务）
            task_desc = "执行Instagram直接关注任务" if self.follow_mode == 'direct' else "执行Instagram关注粉丝任务"
            self.task_progress.emit(self.emulator_id, "instagram", task_desc)
            self._execute_instagram_follow_task()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 关注任务线程执行异常: {str(e)}", component="InstagramFollowTaskThread")
            self.task_failed.emit(self.emulator_id, f"instagram_follow_{self.follow_mode}", str(e))

    def _execute_instagram_follow_task(self):
        """
        执行Instagram关注任务
        ========================================
        功能描述: 创建关注任务实例并执行，替换父类的私信任务执行
        ========================================
        """
        try:
            task_desc = "直接关注" if self.follow_mode == 'direct' else "关注粉丝"
            log_info(f"[模拟器{self.emulator_id}] 开始执行Instagram{task_desc}任务", component="InstagramFollowTaskThread")

            # 🎯 创建Instagram关注任务实例
            from .instagram_follow_task import InstagramFollowTask
            task = InstagramFollowTask(self.emulator_id)

            # 🎯 传递任务开始时间（从线程启动时就开始计时）
            if hasattr(self, 'task_start_time'):
                task.task_start_time = self.task_start_time
                log_info(f"[模拟器{self.emulator_id}] 任务超时计时已从线程启动开始: {task.task_timeout}秒", component="InstagramFollowTaskThread")

            # 🎯 设置关注模式
            task.set_follow_mode(self.follow_mode)

            # 🎯 执行关注任务
            import asyncio
            result = asyncio.run(task.execute())

            # 🎯 处理执行结果
            if result.get('status') == 'completed':
                log_info(f"[模拟器{self.emulator_id}] Instagram{task_desc}任务执行成功", component="InstagramFollowTaskThread")
                self.task_completed.emit(self.emulator_id, f"instagram_follow_{self.follow_mode}", result)
            else:
                error_msg = result.get('message', '未知错误')
                log_error(f"[模拟器{self.emulator_id}] Instagram{task_desc}任务执行失败: {error_msg}", component="InstagramFollowTaskThread")
                self.task_failed.emit(self.emulator_id, f"instagram_follow_{self.follow_mode}", error_msg)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram关注任务执行异常: {str(e)}", component="InstagramFollowTaskThread")
            self.task_failed.emit(self.emulator_id, f"instagram_follow_{self.follow_mode}", str(e))


# 🎯 全局实例 - 只保留一套异步桥梁
_async_bridge = None

def get_async_bridge() -> FixedAsyncBridge:
    """获取全局修复版异步桥梁实例"""
    global _async_bridge
    if _async_bridge is None:
        _async_bridge = FixedAsyncBridge()
    return _async_bridge
