#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Instagram直接关注真实流程测试文件 - ⚡ 快速模式
========================================
功能描述: 使用app_config.json真实参数测试Instagram直接关注流程
测试范围: ⚡ 快速模式 - 直接从阶段四开始，跳过前面步骤节省时间
创建时间: 2025-07-27
作者: AI Assistant

⚡ 快速模式测试流程:
1. 阶段一：环境验证与应用检测 (⚡ 跳过)
2. 阶段二：V2Ray节点连接 (⚡ 跳过)
3. 阶段三：Instagram应用启动 (⚡ 跳过)
4. 阶段四：直接关注业务流程 (✅ 执行)
   - 模式一：直接关注模式（专用批量检测优化）

注意事项:
- 使用app_config.json中的真实参数
- ⚡ 快速模式：假设前面步骤已完成，直接测试直接关注业务
- 节省测试时间，专注于直接关注核心功能
- 包含完整的错误处理和日志记录
- 测试专用批量检测优化的性能提升
"""

import asyncio
import sys
import json
from pathlib import Path
from typing import Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_follow_task import InstagramFollowTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error, log_warning


class RealInstagramDirectFollowTester:
    """Instagram直接关注真实流程测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.instagram_task = None
        self.test_results = {}
        self.start_time = None
        
    def load_real_config(self) -> Dict[str, Any]:
        """加载app_config.json中的真实配置"""
        try:
            log_info(f"[直接关注测试器] 加载app_config.json真实配置")
            
            # 从配置管理器获取真实配置
            real_config = {
                "emulator_id": self.emulator_id,
                "direct_follow_count": self.config_manager.get("instagram_follow.direct_follow_count", 50),
                "fans_follow_count": self.config_manager.get("instagram_follow.fans_follow_count", 50),
                "min_followers": self.config_manager.get("instagram_follow.min_followers", 1),
                "switch_delay_min": self.config_manager.get("instagram_follow.switch_delay_min", 100),
                "switch_delay_max": self.config_manager.get("instagram_follow.switch_delay_max", 2000),
                "follow_delay_min": self.config_manager.get("instagram_follow.follow_delay_min", 100),
                "follow_delay_max": self.config_manager.get("instagram_follow.follow_delay_max", 2000),
                "skip_verified": self.config_manager.get("instagram_follow.skip_verified", True),
                "skip_private": self.config_manager.get("instagram_follow.skip_private", True),
                "all_regions": self.config_manager.get("instagram_follow.all_regions", False),
                "japan": self.config_manager.get("instagram_follow.japan", False),
                "korea": self.config_manager.get("instagram_follow.korea", False),
                "thailand": self.config_manager.get("instagram_follow.thailand", True),
                "follow_users_path": self.config_manager.get("instagram_follow.follow_users_path", "guanzhu.txt")
            }
            
            log_info(f"[直接关注测试器] 真实配置加载完成:")
            log_info(f"[直接关注测试器]   直接关注数量: {real_config['direct_follow_count']}")
            log_info(f"[直接关注测试器]   切换延迟: {real_config['switch_delay_min']}-{real_config['switch_delay_max']}ms")
            log_info(f"[直接关注测试器]   关注延迟: {real_config['follow_delay_min']}-{real_config['follow_delay_max']}ms")
            log_info(f"[直接关注测试器]   地区筛选: 所有地区={real_config['all_regions']}, 日本={real_config['japan']}, 韩国={real_config['korea']}, 泰国={real_config['thailand']}")
            
            return real_config
            
        except Exception as e:
            log_error(f"[直接关注测试器] 加载真实配置失败: {e}")
            return {}

    async def setup_real_test_environment(self) -> bool:
        """设置真实测试环境"""
        try:
            log_info(f"[直接关注测试器] 开始设置真实测试环境")
            
            # 1. 创建Instagram关注任务实例（使用真实配置管理器）
            self.instagram_task = InstagramFollowTask(self.emulator_id)
            
            # 2. 验证模拟器状态
            if not await self._verify_emulator_ready():
                log_error(f"[直接关注测试器] 模拟器{self.emulator_id}未就绪")
                return False
                
            # 3. 验证雷电API
            if not self.instagram_task.is_ld_available():
                log_error(f"[直接关注测试器] 雷电API不可用")
                return False
            
            log_info(f"[直接关注测试器] ✅ 真实测试环境设置完成")
            return True
            
        except Exception as e:
            log_error(f"[直接关注测试器] 设置真实测试环境异常: {e}")
            return False

    async def _verify_emulator_ready(self) -> bool:
        """验证模拟器就绪状态"""
        try:
            log_info(f"[直接关注测试器] 验证模拟器{self.emulator_id}就绪状态")
            
            # 检查雷电API是否可用
            if self.instagram_task.ld is None:
                log_error(f"[直接关注测试器] 雷电API未初始化")
                return False
                
            # 检查模拟器是否运行
            is_running, is_android, _ = self.instagram_task.ld.is_running(self.emulator_id)
            if not is_running or not is_android:
                log_error(f"[直接关注测试器] 模拟器{self.emulator_id}状态异常: 运行={is_running}, Android={is_android}")
                return False
                
            log_info(f"[直接关注测试器] ✅ 模拟器{self.emulator_id}就绪")
            return True
            
        except Exception as e:
            log_error(f"[直接关注测试器] 验证模拟器状态异常: {e}")
            return False

    async def test_stage1_app_detection(self) -> bool:
        """测试阶段一: 环境验证与应用检测"""
        try:
            log_info(f"[直接关注测试器] 开始测试阶段一: 环境验证与应用检测")
            
            # 步骤1：验证模拟器桌面稳定性
            result1 = await self.instagram_task._step_verify_desktop_stable()
            
            # 步骤2：检测应用安装状态
            result2 = await self.instagram_task._step_check_app_installation()
            
            overall_result = result1 and result2
            
            self.test_results["stage1"] = {
                "name": "环境验证与应用检测",
                "result": overall_result,
                "details": {
                    "desktop_stable": result1,
                    "app_installation": result2
                },
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if overall_result:
                log_info(f"[直接关注测试器] ✅ 阶段一测试通过")
            else:
                log_error(f"[直接关注测试器] ❌ 阶段一测试失败")
                
            return overall_result
            
        except Exception as e:
            log_error(f"[直接关注测试器] 阶段一测试异常: {e}")
            self.test_results["stage1"] = {
                "name": "环境验证与应用检测",
                "result": False,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_stage2_v2ray_connection(self) -> bool:
        """测试阶段二: V2Ray节点连接"""
        try:
            log_info(f"[直接关注测试器] 开始测试阶段二: V2Ray节点连接")
            
            # 步骤3：启动V2Ray应用
            result3 = await self.instagram_task._step_app_launch_v2ray()
            
            # 步骤4：检查节点列表状态
            result4 = await self.instagram_task._step_check_node_list()
            
            # 步骤5：连接V2Ray节点
            result5 = await self.instagram_task._step_connect_v2ray_node()
            
            # 步骤6：测试节点延迟
            result6 = await self.instagram_task._step_test_node_latency()
            
            overall_result = result3 and result4 and result5 and result6
            
            self.test_results["stage2"] = {
                "name": "V2Ray节点连接",
                "result": overall_result,
                "details": {
                    "v2ray_launch": result3,
                    "node_list_check": result4,
                    "node_connection": result5,
                    "latency_test": result6
                },
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if overall_result:
                log_info(f"[直接关注测试器] ✅ 阶段二测试通过")
            else:
                log_error(f"[直接关注测试器] ❌ 阶段二测试失败")
                
            return overall_result
            
        except Exception as e:
            log_error(f"[直接关注测试器] 阶段二测试异常: {e}")
            self.test_results["stage2"] = {
                "name": "V2Ray节点连接",
                "result": False,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_stage3_instagram_launch(self) -> bool:
        """测试阶段三: Instagram应用启动"""
        try:
            log_info(f"[直接关注测试器] 开始测试阶段三: Instagram应用启动")
            
            # 步骤7：启动Instagram应用
            result7 = await self.instagram_task._step_app_launch_instagram()
            
            # 步骤8：检测Instagram页面状态
            result8 = await self.instagram_task._step_detect_instagram_page_status()
            
            overall_result = result7 and result8
            
            self.test_results["stage3"] = {
                "name": "Instagram应用启动",
                "result": overall_result,
                "details": {
                    "instagram_launch": result7,
                    "page_status_detection": result8
                },
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            
            if overall_result:
                log_info(f"[直接关注测试器] ✅ 阶段三测试通过")
            else:
                log_error(f"[直接关注测试器] ❌ 阶段三测试失败")
                
            return overall_result
            
        except Exception as e:
            log_error(f"[直接关注测试器] 阶段三测试异常: {e}")
            self.test_results["stage3"] = {
                "name": "Instagram应用启动",
                "result": False,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def test_stage4_direct_follow_business(self) -> bool:
        """测试阶段四: 直接关注业务流程（专用批量检测优化）"""
        try:
            log_info(f"[直接关注测试器] 开始测试阶段四: 直接关注业务流程")

            # 加载真实配置
            real_config = self.load_real_config()
            if not real_config:
                log_error(f"[直接关注测试器] 加载真实配置失败")
                return False

            # 初始化Instagram关注任务（使用真实配置）
            await self.instagram_task.initialize(real_config)

            # 执行模式一：直接关注模式（使用专用批量检测优化）
            log_info(f"[直接关注测试器] 🎯 开始执行模式一：直接关注模式（专用批量检测优化）")
            result = await self.instagram_task._execute_direct_follow_loop()

            self.test_results["stage4"] = {
                "name": "直接关注业务流程",
                "result": result,
                "mode": "模式一：直接关注模式（专用批量检测优化）",
                "stats": {
                    "total_user_followed": self.instagram_task.stats.get('total_user_followed', 0),
                    "skipped_private": self.instagram_task.stats.get('skipped_private', 0),
                    "skipped_verified": self.instagram_task.stats.get('skipped_verified', 0)
                },
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }

            if result:
                log_info(f"[直接关注测试器] ✅ 阶段四测试通过")
                log_info(f"[直接关注测试器] 📊 执行统计:")
                log_info(f"[直接关注测试器]   成功关注: {self.instagram_task.stats.get('total_user_followed', 0)}")
                log_info(f"[直接关注测试器]   跳过私密: {self.instagram_task.stats.get('skipped_private', 0)}")
                log_info(f"[直接关注测试器]   跳过蓝V: {self.instagram_task.stats.get('skipped_verified', 0)}")
            else:
                log_error(f"[直接关注测试器] ❌ 阶段四测试失败")

            return result

        except Exception as e:
            log_error(f"[直接关注测试器] 阶段四测试异常: {e}")
            self.test_results["stage4"] = {
                "name": "直接关注业务流程",
                "result": False,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            return False

    async def run_full_test(self) -> bool:
        """运行完整测试流程"""
        try:
            self.start_time = datetime.now()
            log_info(f"[直接关注测试器] 🚀 开始Instagram直接关注真实流程测试")
            log_info(f"[直接关注测试器] 测试模拟器: {self.emulator_id}")
            log_info(f"[直接关注测试器] 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # 设置测试环境
            if not await self.setup_real_test_environment():
                log_error(f"[直接关注测试器] 测试环境设置失败")
                return False

            # 阶段一：环境验证与应用检测
            stage1_result = await self.test_stage1_app_detection()

            # 阶段二：V2Ray节点连接
            stage2_result = await self.test_stage2_v2ray_connection()

            # 阶段三：Instagram应用启动
            stage3_result = await self.test_stage3_instagram_launch()

            # 阶段四：直接关注业务流程（核心测试）
            stage4_result = await self.test_stage4_direct_follow_business()

            # 计算总体结果
            overall_result = stage1_result and stage2_result and stage3_result and stage4_result

            # 生成测试报告
            await self.generate_test_report(overall_result)

            return overall_result

        except Exception as e:
            log_error(f"[直接关注测试器] 完整测试流程异常: {e}")
            return False

    async def run_quick_test(self) -> bool:
        """运行快速测试流程（⚡ 跳过前面步骤，直接测试直接关注业务）"""
        try:
            self.start_time = datetime.now()
            log_info(f"[直接关注测试器] ⚡ 开始Instagram直接关注快速测试")
            log_info(f"[直接关注测试器] 测试模拟器: {self.emulator_id}")
            log_info(f"[直接关注测试器] 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            log_info(f"[直接关注测试器] ⚡ 快速模式：跳过阶段一、二、三，直接测试阶段四")

            # 设置测试环境
            if not await self.setup_real_test_environment():
                log_error(f"[直接关注测试器] 测试环境设置失败")
                return False

            # ⚡ 快速模式：直接测试阶段四
            stage4_result = await self.test_stage4_direct_follow_business()

            # 生成测试报告
            await self.generate_test_report(stage4_result, quick_mode=True)

            return stage4_result

        except Exception as e:
            log_error(f"[直接关注测试器] 快速测试流程异常: {e}")
            return False

    async def generate_test_report(self, overall_result: bool, quick_mode: bool = False) -> None:
        """生成测试报告"""
        try:
            end_time = datetime.now()
            duration = end_time - self.start_time

            log_info(f"[直接关注测试器] " + "="*80)
            log_info(f"[直接关注测试器] 📋 Instagram直接关注测试报告")
            log_info(f"[直接关注测试器] " + "="*80)
            log_info(f"[直接关注测试器] 测试模式: {'⚡ 快速模式' if quick_mode else '🔄 完整模式'}")
            log_info(f"[直接关注测试器] 测试模拟器: {self.emulator_id}")
            log_info(f"[直接关注测试器] 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            log_info(f"[直接关注测试器] 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            log_info(f"[直接关注测试器] 总耗时: {duration}")
            log_info(f"[直接关注测试器] 总体结果: {'✅ 通过' if overall_result else '❌ 失败'}")
            log_info(f"[直接关注测试器] " + "-"*80)

            # 详细测试结果
            for stage_key, stage_data in self.test_results.items():
                status = "✅ 通过" if stage_data["result"] else "❌ 失败"
                log_info(f"[直接关注测试器] {stage_data['name']}: {status} ({stage_data['timestamp']})")

                if "details" in stage_data:
                    for detail_key, detail_result in stage_data["details"].items():
                        detail_status = "✅" if detail_result else "❌"
                        log_info(f"[直接关注测试器]   - {detail_key}: {detail_status}")

                if "stats" in stage_data:
                    log_info(f"[直接关注测试器]   📊 执行统计:")
                    for stat_key, stat_value in stage_data["stats"].items():
                        log_info(f"[直接关注测试器]     {stat_key}: {stat_value}")

                if "error" in stage_data:
                    log_info(f"[直接关注测试器]   ❌ 错误: {stage_data['error']}")

            log_info(f"[直接关注测试器] " + "="*80)

            # 保存测试报告到文件
            report_data = {
                "test_mode": "quick" if quick_mode else "full",
                "emulator_id": self.emulator_id,
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration.total_seconds(),
                "overall_result": overall_result,
                "test_results": self.test_results
            }

            report_file = f"test_report_direct_follow_{self.emulator_id}_{self.start_time.strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            log_info(f"[直接关注测试器] 📄 测试报告已保存: {report_file}")

        except Exception as e:
            log_error(f"[直接关注测试器] 生成测试报告异常: {e}")


async def main():
    """主函数"""
    try:
        # 创建测试器实例
        tester = RealInstagramDirectFollowTester(emulator_id=2)

        # 运行快速测试（⚡ 推荐模式）
        log_info(f"开始Instagram直接关注真实流程测试")
        result = await tester.run_quick_test()

        if result:
            log_info(f"🎉 Instagram直接关注测试完成 - 结果: ✅ 成功")
        else:
            log_error(f"🎉 Instagram直接关注测试完成 - 结果: ❌ 失败")

        return result

    except Exception as e:
        log_error(f"主函数执行异常: {e}")
        return False


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
