#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Instagram关注任务执行器 - 完整自动化关注流程管理
========================================
功能描述: Instagram关注任务的具体实现，支持两种关注模式的完整自动化流程

模块结构:
1. Instagram关注任务执行器 (InstagramFollowTask)
2. 配置管理系统 (配置加载、热加载、观察者模式)
3. 任务执行系统 (四阶段执行流程)
4. 双模式关注系统 (直接关注、关注粉丝)

主要功能:
- 任务配置管理: 参数加载、热加载、默认配置
- 执行流程控制: 四阶段流程、错误处理、状态管理
- 原生API集成: 雷电模拟器API、应用操作、UI自动化
- 双模式关注: 直接关注目标用户、关注目标用户的粉丝
- 智能筛选: 蓝V用户跳过、私密用户跳过、去重机制

关注模式详解:
- 直接关注模式: 从guanzhu.txt文件读取用户列表，逐个搜索并关注目标用户
- 关注粉丝模式: 从guanzhu.txt文件读取目标用户，打开其粉丝列表，批量关注粉丝

技术特点:
- 架构继承: 100%继承InstagramDMTask的完整架构和统一设置
- 智能去重: 自动记录已关注用户，避免重复关注
- 筛选机制: 支持跳过蓝V认证用户和私密账户
- 延迟控制: 100-2000毫秒可调延迟，避免被检测

调用关系: 被异步桥梁调用，执行具体的Instagram关注业务逻辑
注意事项: 使用雷电模拟器原生API和图像识别引擎，包含完整的错误处理和重试机制
参考文档: Instagram关注功能开发任务清单.md
========================================
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from .instagram_task import InstagramDMTask
from .logger_manager import log_info, log_error, log_warning

# ============================================================================
# 🎯 1. Instagram关注任务执行器
# ============================================================================
# 功能描述: Instagram关注任务的核心执行器，管理完整的自动化关注流程
# 调用关系: 被异步桥梁调用，作为Instagram关注任务的统一入口点
# 注意事项: 集成配置管理、原生API、执行控制等多个子系统，支持双模式关注
# ============================================================================

class InstagramFollowTask(InstagramDMTask):
    """🎯 Instagram关注任务执行器 - 完整自动化关注流程管理"""

    def __init__(self, emulator_id: int):
        """初始化Instagram关注任务执行器"""

        # ========================================================================
        # 🎯 1.1 基础参数初始化 - 完全继承父类
        # ========================================================================

        # ✅ 完全继承父类初始化，自动获得所有统一设置
        super().__init__(emulator_id)

        # ========================================================================
        # 🎯 1.2 关注任务状态管理
        # ========================================================================

        # 🎯 关注任务执行状态（参考代码一致）
        self.stats = {
            'total_followed': 0,         # 关注用户粉丝列表的数量统计
            'total_user_followed': 0,    # 直接关注用户的数量统计
            'skipped_private': 0,        # 跳过的私密用户数
            'skipped_verified': 0        # 跳过的蓝V用户数
        }
        self.current_target_user = ""    # 当前目标用户

        # 🎯 关注模式配置
        self.follow_mode = "direct"      # 关注模式：direct=直接关注, fans=关注粉丝

        # ========================================================================
        # 🎯 1.3 关注任务配置初始化
        # ========================================================================

        # 🎯 重新加载关注任务配置（替换私信配置）
        self._load_follow_config()

        log_info(f"[模拟器{self.emulator_id}] Instagram关注任务执行器初始化完成", component="InstagramFollowTask")

    def _emit_progress_update(self):
        """发送Instagram粉丝关注任务状态更新信号到UI"""
        try:
            # 🎯 使用专用的Instagram任务状态更新信号
            from core.status_converter import InstagramFollowStatus
            from core.logger_manager import get_logger_manager

            # 🎯 根据任务进度生成状态信息
            if self.stats['total_followed'] < self.fans_follow_count:
                task_status = InstagramFollowStatus.format_in_progress(self.stats['total_followed'], self.fans_follow_count)
            else:
                task_status = InstagramFollowStatus.format_completed(self.stats['total_followed'], self.fans_follow_count)

            # 🎯 通过专用信号发送Instagram关注任务状态更新
            logger_manager = get_logger_manager()
            logger_manager.instagram_task_status_updated.emit(self.emulator_id, task_status)

            log_info(f"[模拟器{self.emulator_id}] Instagram关注任务状态更新信号已发送: {task_status}", component="InstagramFollowTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 发送Instagram关注任务状态更新失败: {e}", component="InstagramFollowTask")

    def _emit_direct_follow_progress_update(self):
        """发送Instagram直接关注任务状态更新信号到UI"""
        try:
            # 🎯 使用专用的Instagram任务状态更新信号
            from core.status_converter import InstagramFollowStatus
            from core.logger_manager import get_logger_manager

            # 🎯 根据直接关注任务进度生成状态信息
            if self.stats['total_user_followed'] < self.direct_follow_count:
                task_status = InstagramFollowStatus.format_in_progress(self.stats['total_user_followed'], self.direct_follow_count)
            else:
                task_status = InstagramFollowStatus.format_completed(self.stats['total_user_followed'], self.direct_follow_count)

            # 🎯 通过专用信号发送Instagram关注任务状态更新
            logger_manager = get_logger_manager()
            logger_manager.instagram_task_status_updated.emit(self.emulator_id, task_status)

            log_info(f"[模拟器{self.emulator_id}] 直接关注任务状态更新信号已发送: {task_status}", component="InstagramFollowTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 发送直接关注任务状态更新失败: {e}", component="InstagramFollowTask")

    # ------------------------------------------------------------------------
    # 🎯 2. 关注模式配置
    # ------------------------------------------------------------------------
    # 功能描述: 设置关注任务的执行模式，支持两种不同的关注策略
    # 调用关系: 被异步桥梁调用，在任务执行前设置关注模式
    # 注意事项: 必须在execute()方法调用前设置，影响整个关注流程的执行逻辑
    # ------------------------------------------------------------------------

    def set_follow_mode(self, mode: str):
        """🎯 设置关注模式"""
        if mode in ["direct", "fans"]:
            self.follow_mode = mode
            log_info(f"[模拟器{self.emulator_id}] 关注模式已设置为: {mode}", component="InstagramFollowTask")
        else:
            log_warning(f"[模拟器{self.emulator_id}] 无效的关注模式: {mode}，保持默认模式: {self.follow_mode}",
                       component="InstagramFollowTask")

    # ------------------------------------------------------------------------
    # 🎯 2.1 配置管理系统
    # ------------------------------------------------------------------------
    # 功能描述: 实现关注任务配置的加载功能，支持运行时动态更新配置参数
    # 调用关系: 通过观察者模式监听配置变化，自动更新任务参数
    # 注意事项: 只处理instagram_follow.*相关配置，避免不必要的重载操作
    # ------------------------------------------------------------------------

    def _load_follow_config(self):
        """🎯 加载关注任务配置"""
        try:
            # 🎯 基础任务参数
            self.direct_follow_count = self.config_manager.get("instagram_follow.direct_follow_count", 50)
            self.fans_follow_count = self.config_manager.get("instagram_follow.fans_follow_count", 50)
            self.min_followers = self.config_manager.get("instagram_follow.min_followers", 1)
            
            # 🎯 延迟时间参数
            self.switch_delay_min = self.config_manager.get("instagram_follow.switch_delay_min", 100)
            self.switch_delay_max = self.config_manager.get("instagram_follow.switch_delay_max", 2000)
            self.follow_delay_min = self.config_manager.get("instagram_follow.follow_delay_min", 100)
            self.follow_delay_max = self.config_manager.get("instagram_follow.follow_delay_max", 2000)

            # 🎯 任务超时参数（从UI界面的"任务总超时时间"获取，默认900秒=15分钟）
            self.task_timeout = self.config_manager.get("basic_config.task_timeout_seconds", 900)
            
            # 🎯 筛选控制参数
            self.skip_verified = self.config_manager.get("instagram_follow.skip_verified", True)
            self.skip_private = self.config_manager.get("instagram_follow.skip_private", True)
            
            # 🎯 地区选择参数
            self.all_regions = self.config_manager.get("instagram_follow.all_regions", True)
            self.japan = self.config_manager.get("instagram_follow.japan", False)
            self.korea = self.config_manager.get("instagram_follow.korea", False)
            self.thailand = self.config_manager.get("instagram_follow.thailand", False)
            
            # 🎯 文件路径参数
            self.follow_users_path = self.config_manager.get("instagram_follow.follow_users_path", "guanzhu.txt")
            
            log_info(f"[模拟器{self.emulator_id}] Instagram关注任务配置加载完成", component="InstagramFollowTask")
            
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载Instagram关注任务配置失败: {e}", component="InstagramFollowTask")

    def _on_config_changed(self, key: str, old_value, new_value):
        """🎯 配置变化处理回调"""
        try:
            # 🎯 只处理Instagram关注相关的配置
            if not key.startswith("instagram_follow."):
                return

            log_info(f"[模拟器{self.emulator_id}] 检测到Instagram关注配置变化: {key} = {old_value} → {new_value}", 
                    component="InstagramFollowTask")

            # 🎯 重新加载关注配置
            self._load_follow_config()

            log_info(f"[模拟器{self.emulator_id}] Instagram关注任务配置已热加载更新", component="InstagramFollowTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 处理关注配置变化失败: {str(e)}", component="InstagramFollowTask")

    # ------------------------------------------------------------------------
    # 🎯 3. 关注任务主执行流程
    # ------------------------------------------------------------------------
    # 功能描述: Instagram关注任务的主执行方法，复用前三阶段，替换第四阶段
    # 调用关系: 被异步桥梁调用，作为关注任务的统一入口点
    # 注意事项: 支持两种关注模式，完全复用InstagramDMTask的前三阶段流程
    # ------------------------------------------------------------------------

    async def execute(self) -> Dict[str, Any]:
        """步骤1-15：执行Instagram关注任务主流程"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行Instagram关注任务", component="InstagramFollowTask")

            # 🎯 检查任务开始时间（应该由线程传递，如果没有则设置）
            if not hasattr(self, 'task_start_time') or self.task_start_time is None:
                self.task_start_time = time.time()
                log_warning(f"[模拟器{self.emulator_id}] 任务开始时间未由线程传递，在此设置", component="InstagramFollowTask")

            elapsed_time = time.time() - self.task_start_time
            log_info(f"[模拟器{self.emulator_id}] 任务超时设置: {self.task_timeout}秒，已运行: {elapsed_time:.2f}秒", component="InstagramFollowTask")

            # ========================================================================
            # ✅ 阶段一：环境验证与应用检测 (完全继承InstagramDMTask)
            # ========================================================================
            
            # 🎯 步骤1：验证模拟器桌面稳定性
            if not await self._step_verify_desktop_stable():
                return {'status': 'failed', 'message': '模拟器桌面验证失败'}

            # 🎯 步骤2：检测应用安装状态
            if not await self._step_check_app_installation():
                return {'status': 'failed', 'message': '应用检测失败'}

            # ========================================================================
            # ✅ 阶段二：V2Ray节点连接 (完全继承InstagramDMTask)
            # ========================================================================

            # 🎯 步骤3：启动V2Ray应用
            if not await self._step_app_launch_v2ray():
                return {'status': 'failed', 'message': 'V2Ray应用启动失败'}

            # 🎯 步骤4：检查节点列表状态
            if not await self._step_check_node_list():
                return {'status': 'failed', 'message': 'V2Ray节点列表检查失败'}

            # 🎯 步骤5：连接V2Ray节点
            if not await self._step_connect_v2ray_node():
                return {'status': 'failed', 'message': 'V2Ray节点连接失败'}

            # 🎯 步骤6：测试节点延迟
            if not await self._step_test_node_latency():
                return {'status': 'failed', 'message': 'V2Ray节点延迟测试失败'}

            # ========================================================================
            # ✅ 阶段三：Instagram应用启动 (完全继承InstagramDMTask)
            # ========================================================================
            
            # 🎯 步骤7：启动Instagram应用
            if not await self._step_app_launch_instagram():
                return {'status': 'failed', 'message': 'Instagram应用启动失败'}

            # 🎯 步骤8：检测Instagram页面状态（三次重启机制）
            page_status = await self._step_detect_instagram_page_status()
            log_info(f"[模拟器{self.emulator_id}] Instagram页面状态检测结果: {page_status}", component="InstagramFollowTask")

            # 根据页面状态分类进行处理
            if page_status == "正常-在主页面":
                log_info(f"[模拟器{self.emulator_id}] ✅ Instagram已在主页面，可以继续执行任务", component="InstagramFollowTask")
            elif page_status.startswith("任务终止-"):
                # 任务已终止，模拟器已关闭
                log_error(f"[模拟器{self.emulator_id}] ❌ Instagram任务终止", component="InstagramFollowTask")
                return {'status': 'failed', 'message': page_status}
            else:
                # 其他未知状态
                log_error(f"[模拟器{self.emulator_id}] ❌ Instagram状态未知，任务终止", component="InstagramFollowTask")
                return {'status': 'failed', 'message': f'Instagram状态未知: {page_status}'}

            # ====================================================================
            # � 阶段四：关注任务执行
            # ====================================================================

            # 🎯 步骤11：执行关注任务
            if not await self._execute_follow_task():
                return {'status': 'failed', 'message': '关注任务执行失败'}

            # ========================================================================
            # ✅ 任务完成处理 (完全继承InstagramDMTask)
            # ========================================================================
            
            # 🎯 注销配置观察者
            self.unregister_config_observer()

            # 🎯 任务完成后关闭模拟器并移除心跳监控
            await self._cleanup_after_task_completion()

            return {
                'status': 'completed',
                'message': 'Instagram关注任务执行完成',
                'total_followed': self.stats['total_followed'],
                'total_user_followed': self.stats['total_user_followed'],
                'skipped_verified': self.stats['skipped_verified'],
                'skipped_private': self.stats['skipped_private']
            }

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram关注任务异常: {str(e)}", component="InstagramFollowTask")
            # ✅ 异常处理 (完全继承InstagramDMTask)
            self.unregister_config_observer()
            return {'status': 'failed', 'message': f'任务执行异常: {str(e)}'}

    # ------------------------------------------------------------------------
    # 🎯 11 关注任务分发器
    # ------------------------------------------------------------------------
    # 功能描述: 根据关注模式选择执行直接关注或关注粉丝流程
    # 调用关系: 被主执行流程的步骤11调用
    # 注意事项: 支持direct和fans两种关注模式
    # ------------------------------------------------------------------------

    async def _execute_follow_task(self) -> bool:
        """步骤11：执行关注任务 - 双模式分发"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行关注任务，模式: {self.follow_mode}", component="InstagramFollowTask")

            # 🎯 根据关注模式执行不同的业务逻辑
            if self.follow_mode == "direct":
                return await self._execute_direct_follow()
            elif self.follow_mode == "fans":
                return await self._execute_fans_follow()
            else:
                log_error(f"[模拟器{self.emulator_id}] 未知的关注模式: {self.follow_mode}", component="InstagramFollowTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 执行关注任务失败: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
# ============================================================================
# 🎯 4. 关注业务逻辑系统（完全按照参考代码实现）
# ============================================================================
# 功能描述: Instagram关注任务的核心业务逻辑，支持两种关注模式
# 调用关系: 被阶段四执行流程调用，实现具体的关注操作逻辑
# 注意事项: 完全按照参考代码的逻辑实现，确保行为一致性和稳定性
# 模式说明: 支持直接关注模式和关注粉丝模式两种不同的关注策略
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 4.1 关注模式执行入口
    # ------------------------------------------------------------------------
    # 功能描述: 两种关注模式的执行入口，负责模式选择和流程控制
    # 调用关系: 被阶段四执行流程调用，根据配置选择对应的关注模式
    # 注意事项: 两种模式使用相同的初始化流程，但执行逻辑完全不同
    # 模式区别: 直接关注用户本人 vs 关注用户的粉丝列表
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.1.1 模式一：直接关注模式执行流程
    # ------------------------------------------------------------------------
    # 功能描述: 从guanzhu.txt读取用户名，逐个搜索并关注目标用户本人
    # 调用关系: 被阶段四执行流程调用，当follow_mode为"direct"时执行
    # 注意事项: 关注的是目标用户本人，不涉及粉丝列表操作
    # 适用场景: 需要关注特定的目标用户账号，建立直接关注关系
    # ------------------------------------------------------------------------

    async def _execute_direct_follow(self) -> bool:
        """🎯 模式一：直接关注模式执行流程"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行【模式一：直接关注模式】", component="InstagramFollowTask")

            # 🎯 步骤1：执行直接关注循环
            return await self._execute_direct_follow_loop()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 【模式一：直接关注模式】执行失败: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.1.2 模式二：关注粉丝模式执行流程
    # ------------------------------------------------------------------------
    # 功能描述: 从guanzhu.txt读取目标用户，打开其粉丝列表，批量关注粉丝
    # 调用关系: 被阶段四执行流程调用，当follow_mode为"fans"时执行
    # 注意事项: 关注的是目标用户的粉丝，而不是目标用户本人
    # 适用场景: 需要关注特定用户的粉丝群体，扩大关注范围和影响力
    # ------------------------------------------------------------------------

    async def _execute_fans_follow(self) -> bool:
        """🎯 模式二：关注粉丝模式执行流程"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行【模式二：关注粉丝模式】", component="InstagramFollowTask")

            # 🎯 步骤1：执行关注粉丝循环
            return await self._execute_fans_follow_loop()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 【模式二：关注粉丝模式】执行失败: {e}", component="InstagramFollowTask")
            return False



    # ------------------------------------------------------------------------
    # 🎯 4.3 模式一：直接关注循环步骤
    # ------------------------------------------------------------------------
    # 功能描述: 模式一的核心循环逻辑，逐个处理用户并关注目标用户本人
    # 调用关系: 被模式一执行流程调用，实现具体的关注循环操作
    # 注意事项: 关注的是目标用户本人，支持去重和超时控制
    # 技术实现: 边读边删机制获取用户，动态坐标点击关注按钮
    # ------------------------------------------------------------------------

    async def _execute_direct_follow_loop(self) -> bool:
        """🎯 模式一：直接关注循环执行流程"""
        try:
            import time
            task_start_time = time.time()

            log_info(f"[模拟器{self.emulator_id}] 开始【模式一：直接关注循环】，目标关注数: {self.direct_follow_count}",
                    component="InstagramFollowTask")

            processed_count = 0  # 添加独立的处理计数器
            while self.stats['total_user_followed'] < self.direct_follow_count and processed_count < self.direct_follow_count * 3 and not self.stop_flag:
                # 🎯 步骤1：检查总任务超时（参考代码实现）
                if time.time() - self.task_start_time >= self.task_timeout:
                    log_info(f"[模拟器{self.emulator_id}] 总任务超时({self.task_timeout}秒).任务进度: {self.stats['total_user_followed']} / {self.direct_follow_count} 耗时: {time.time() - self.task_start_time:.2f}秒",
                            component="InstagramFollowTask")
                    break

                # 🎯 步骤2：动态获取下一个关注用户（从guanzhu.txt边读边删）
                username = self._get_next_follow_user()
                if not username:
                    log_info(f"[模拟器{self.emulator_id}] 目标用户列表已耗尽. --任务进度 :{self.stats['total_user_followed']} / {self.direct_follow_count} 耗时: {time.time() - task_start_time:.2f}秒",
                            component="InstagramFollowTask")
                    break

                processed_count += 1  # 递增处理计数器
                log_info(f"[模拟器{self.emulator_id}] 开始处理目标用户: {username} ({processed_count}/{self.direct_follow_count})",
                        component="InstagramFollowTask")

                # 🎯 步骤3：执行单个用户的关注流程（关注用户本人）
                await self.follow_user(username)

                # 🎯 步骤4：检查是否达到目标数量
                if self.stats['total_user_followed'] >= self.direct_follow_count:
                    log_info(f"[模拟器{self.emulator_id}] 任务完成,退出循环.--任务进度 :{self.stats['total_user_followed']} / {self.direct_follow_count} 耗时: {time.time() - task_start_time:.2f}秒",
                            component="InstagramFollowTask")
                    break

                # 🎯 步骤5：切换用户延迟（避免操作过于频繁）
                import random
                # 确保延迟参数顺序正确，防止randrange错误
                min_switch_delay = min(self.switch_delay_min, self.switch_delay_max)
                max_switch_delay = max(self.switch_delay_min, self.switch_delay_max)
                delay_ms = random.randint(min_switch_delay, max_switch_delay)
                delay_seconds = delay_ms / 1000.0  # 转换为秒
                log_info(f"[模拟器{self.emulator_id}] ⏱️ 切换用户延迟: {delay_ms}毫秒", component="InstagramFollowTask")
                await asyncio.sleep(delay_seconds)

            # 🎯 直接关注循环完成
            log_info(f"[模拟器{self.emulator_id}] 【模式一：直接关注循环】完成，成功关注: {self.stats['total_user_followed']}, 跳过蓝V: {self.stats['skipped_verified']}, 跳过私密: {self.stats['skipped_private']}",
                    component="InstagramFollowTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 【模式一：直接关注循环】执行失败: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.4 模式二：关注粉丝循环步骤
    # ------------------------------------------------------------------------
    # 功能描述: 模式二的核心循环逻辑，逐个处理目标用户并关注其粉丝列表
    # 调用关系: 被模式二执行流程调用，实现具体的粉丝关注循环操作
    # 注意事项: 关注的是目标用户的粉丝，而不是目标用户本人
    # 技术实现: 打开粉丝列表，批量关注可见粉丝，支持切换用户延迟
    # ------------------------------------------------------------------------

    async def _execute_fans_follow_loop(self) -> bool:
        """🎯 模式二：关注粉丝循环执行流程"""
        try:
            import time
            task_start_time = time.time()

            log_info(f"[模拟器{self.emulator_id}] 开始【模式二：关注粉丝循环】，目标关注数: {self.fans_follow_count}",
                    component="InstagramFollowTask")

            while self.stats['total_followed'] <= self.fans_follow_count and not self.stop_flag:

                # 🎯 步骤1：检查总任务超时（参考代码实现）
                if time.time() - self.task_start_time >= self.task_timeout:
                    log_info(f"[模拟器{self.emulator_id}] 总任务超时({self.task_timeout}秒).任务进度: {self.stats['total_followed']} / {self.fans_follow_count} 耗时: {time.time() - self.task_start_time:.2f}秒",
                            component="InstagramFollowTask")
                    break

                # 🎯 步骤2：动态获取下一个目标用户（边读边删机制）
                target_user = self._get_next_follow_user()
                if not target_user:
                    log_info(f"[模拟器{self.emulator_id}] 📝 目标用户列表已耗尽，任务结束", component="InstagramFollowTask")
                    break

                # 设置当前目标用户（用于状态跟踪）
                self.current_target_user = target_user
                log_info(f"[模拟器{self.emulator_id}] 👥 开始处理目标用户: {target_user} (关注其粉丝)", component="InstagramFollowTask")

                # 🎯 步骤3：执行目标用户的粉丝关注流程
                result = await self.enter_followers_list(target_user)

                # 🎯 步骤4：处理关注结果，检查任务完成状态
                if result and "已完成任务" in result:
                    log_info(f"[模拟器{self.emulator_id}] 🎉 完成目标用户 {target_user} 的粉丝关注: {result}", component="InstagramFollowTask")
                    break
                elif result:
                    log_info(f"[模拟器{self.emulator_id}] 📊 目标用户 {target_user} 处理结果: {result}", component="InstagramFollowTask")
                else:
                    log_warning(f"[模拟器{self.emulator_id}] ❌ 目标用户 {target_user} 的粉丝关注失败", component="InstagramFollowTask")

                # 🎯 步骤5：检查是否达到目标数量
                if self.stats['total_followed'] >= self.fans_follow_count:
                    log_info(f"[模拟器{self.emulator_id}] 任务完成,退出循环. 任务进度: {self.stats['total_followed']} / {self.fans_follow_count} 耗时: {time.time() - task_start_time:.2f}秒",
                            component="InstagramFollowTask")
                    break

                # 🎯 步骤6：切换用户延迟（避免操作过于频繁）
                import random
                # 确保延迟参数顺序正确，防止randrange错误
                min_switch_delay = min(self.switch_delay_min, self.switch_delay_max)
                max_switch_delay = max(self.switch_delay_min, self.switch_delay_max)
                delay_ms = random.randint(min_switch_delay, max_switch_delay)
                delay_seconds = delay_ms / 1000.0  # 转换为秒
                log_info(f"[模拟器{self.emulator_id}] ⏱️ 切换用户延迟: {delay_ms}毫秒", component="InstagramFollowTask")
                await asyncio.sleep(delay_seconds)

            # 🎯 关注粉丝循环完成
            log_info(f"[模拟器{self.emulator_id}] ✅ 【模式二：关注粉丝循环】完成，成功关注: {self.stats['total_followed']}, 跳过蓝V: {self.stats['skipped_verified']}, 跳过私密: {self.stats['skipped_private']}",
                    component="InstagramFollowTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] ❌ 【模式二：关注粉丝循环】执行失败: {e}", component="InstagramFollowTask")
            return False

# ============================================================================
# 🎯 5. 核心关注方法系统（完全按照参考代码实现）
# ============================================================================
# 功能描述: Instagram关注任务的核心关注方法，实现具体的关注操作逻辑
# 调用关系: 被关注循环调用，执行单个用户或粉丝列表的关注操作
# 注意事项: 完全按照参考代码的逻辑实现，使用动态坐标点击机制
# 技术实现: 支持多分辨率适配，使用UI检测和坐标计算的关注方式
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 5.1 单用户关注处理步骤
    # ------------------------------------------------------------------------
    # 功能描述: 处理单个用户的完整关注流程，适用于模式一的直接关注
    # 调用关系: 被直接关注循环调用，实现对目标用户本人的关注操作
    # 注意事项: 包含用户页面跳转、状态检测、关注按钮点击等完整流程
    # 技术实现: 使用Instagram deep link跳转，动态UI检测和坐标点击
    # ------------------------------------------------------------------------

    async def follow_user(self, user_id: str) -> bool:
        """🎯 直接关注专用：使用批量检测优化的用户关注流程"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始处理用户：{user_id}", component="InstagramFollowTask")

            # 🎯 步骤1：执行跳转命令
            success, output = self.ld.execute_ld(
                self.emulator_id,
                f'am start -a android.intent.action.VIEW -d "instagram://user?username={user_id}"'
            )

            if not success:
                return False

            log_info(f"[模拟器{self.emulator_id}] 跳转到用户主页：{user_id}", component="InstagramFollowTask")

            # 🎯 步骤2：使用专用的批量检测方法（不影响粉丝关注任务）
            profile_data = await self._direct_follow_batch_detect(user_id)
            if not profile_data or not profile_data.get("page_loaded"):
                log_error(f"[模拟器{self.emulator_id}] 批量检测失败", component="InstagramFollowTask")
                return False

            print("已跳转至用户资料页,批量检测已完成")

            # 🎯 步骤3：基于批量检测结果进行判断


            if profile_data.get("is_private"):
                log_info(f"[模拟器{self.emulator_id}] 📊 目标用户处理结果: 私密账户", component="InstagramFollowTask")
                self.stats['skipped_private'] += 1
                return False

            if profile_data.get("is_verified"):
                log_info(f"[模拟器{self.emulator_id}] 📊 目标用户处理结果: 蓝V认证账户", component="InstagramFollowTask")
                self.stats['skipped_verified'] += 1
                return False

            print("普通用户,检查关注状态")

            if profile_data.get("is_followed"):
                log_info(f"[模拟器{self.emulator_id}] 该用户已关注,跳过", component="InstagramFollowTask")
                return False

            # 🎯 步骤4：执行关注操作（使用坐标点击）
            if profile_data.get("follow_button_pos"):
                log_info(f"[模拟器{self.emulator_id}] 该用户未关注,开始关注", component="InstagramFollowTask")

                x, y = profile_data["follow_button_pos"]

                # 使用坐标点击
                click_result = self.ld.touch(self.emulator_id, x, y)

                # touch方法返回空字符串表示成功，需要验证实际效果
                if click_result is not None and click_result != False:
                    # 等待界面响应
                    import asyncio
                    await asyncio.sleep(1)

                    # 验证关注是否成功
                    after_click_node = self.ld.find_node(resource_id="com.instagram.android:id/profile_header_follow_button")
                    if after_click_node and after_click_node.get('text', '') in ["已关注", "已请求"]:
                        # 关注成功
                        self.stats['total_user_followed'] += 1
                        log_info(f"[模拟器{self.emulator_id}] 关注完成,已关注 {self.stats['total_user_followed']} / {self.direct_follow_count}",
                                component="InstagramFollowTask")

                        # 🎯 发送直接关注任务状态更新信号到UI
                        self._emit_direct_follow_progress_update()

                        if self.stats['total_user_followed'] >= self.direct_follow_count:
                            print("任务完成,退出循环")
                            return True
                        return True
                    else:
                        log_error(f"[模拟器{self.emulator_id}] 关注失败", component="InstagramFollowTask")
                        return False
                else:
                    log_error(f"[模拟器{self.emulator_id}] 点击操作失败", component="InstagramFollowTask")
                    return False
            else:
                log_warning(f"[模拟器{self.emulator_id}] 未找到关注按钮坐标", component="InstagramFollowTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 关注用户 {user_id} 异常: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 5.2 粉丝列表关注处理步骤
    # ------------------------------------------------------------------------
    # 功能描述: 处理目标用户粉丝列表的关注流程，适用于模式二的粉丝关注
    # 调用关系: 被关注粉丝循环调用，实现对目标用户粉丝的批量关注操作
    # 注意事项: 包含用户页面跳转、粉丝列表打开、批量关注等完整流程
    # 技术实现: 使用滚动加载机制，动态获取可见粉丝并逐个关注
    # ------------------------------------------------------------------------

    async def enter_followers_list(self, user_id: str) -> str:
        """🎯 关注用户粉丝列表（参考代码一致的逻辑）"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始处理用户：{user_id}", component="InstagramFollowTask")

            # 🎯 步骤1：跳转到用户资料页
            if not await self.navigate_to_profile(user_id):
                return "跳转失败"

            print("已跳转至用户资料页,检测是否私密账户")

            # 🎯 步骤2：检查是否为私密账户（不检查蓝V用户）
            is_private = await self.is_private_account()
            if is_private:
                log_info(f"[模拟器{self.emulator_id}] 用户 {user_id} 是私密账户，跳过", component="InstagramFollowTask")
                return "私密账户"

            print("普通用户,检查粉丝数")

            # 🎯 步骤3：检查粉丝数
            if not await self.check_followers_count():
                return "粉丝数不符合要求"

            print(f"用户资料: {user_id}")

            # 🎯 步骤4：处理粉丝列表
            return await self.process_followers_list()

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 进入粉丝列表异常: {e}", component="InstagramFollowTask")
            return "异常"

    async def process_followers_list(self) -> str:
        """处理粉丝列表（参考代码一致的逻辑）"""
        try:
            import time
            import random

            # 🎯 步骤1：打开粉丝列表
            if not await self._step_open_followers_list():
                return '没有打开粉丝列表'

            start_time = time.time()

            # 🎯 步骤2：开始粉丝关注循环
            while not self.stop_flag and self.stats['total_followed'] < self.fans_follow_count:

                # 🎯 步骤3：检查总任务超时（参考代码实现）
                if time.time() - self.task_start_time >= self.task_timeout:
                    log_info(f"[模拟器{self.emulator_id}] 总任务超时({self.task_timeout}秒)，退出", component="InstagramFollowTask")
                    return f"总任务超时({self.task_timeout}秒)，退出"

                # 🎯 步骤4-7：批量检测所有UI元素（基于私信任务的成熟实现）
                ui_data = await self._batch_detect_all_ui_elements()
                if not ui_data:
                    log_error(f"[模拟器{self.emulator_id}] 批量检测失败，退出任务", component="InstagramFollowTask")
                    return "UI检测失败，退出任务"

                # 检查频繁提示
                if ui_data.get("retry_prompt"):
                    log_info(f"[模拟器{self.emulator_id}] 出现稍后重试,关注频繁,终止任务", component="InstagramFollowTask")
                    return "出现稍后重试,关注频繁,终止任务"
                if ui_data.get("review_prompt"):
                    log_info(f"[模拟器{self.emulator_id}] 出现请求审核,关注频繁,终止任务", component="InstagramFollowTask")
                    return "出现请求审核,关注频繁,终止任务"

                # 尝试点击查看更多
                if ui_data.get("view_more_button"):
                    log_info(f"[模拟器{self.emulator_id}] 发现查看更多按钮，点击成功", component="InstagramFollowTask")
                    查看更多 = self.ld.find_node(text="查看更多")
                    if 查看更多:
                        self.ld.click_node(查看更多)
                        await asyncio.sleep(1)
                        continue

                # 检查是否到达底部
                if ui_data.get("reached_bottom"):
                    log_info(f"[模拟器{self.emulator_id}] 检测到底部标识，停止任务", component="InstagramFollowTask")
                    break

                # 获取粉丝信息
                followers = ui_data.get("followers", [])
                if not followers:
                    return ''

                # 🎯 步骤8：遍历粉丝列表进行关注
                for follower in followers:
                    # 8.1 检查用户是否符合筛选条件
                    if await self.should_follow(follower['nickname']):
                        log_info(f"[模拟器{self.emulator_id}] 用户: {follower['nickname']} 符合地区筛选", component="InstagramFollowTask")

                        # 8.2 检查用户是否已关注
                        if follower['is_followed']:
                            log_info(f"[模拟器{self.emulator_id}] 用户: {follower['nickname']} 已关注", component="InstagramFollowTask")
                            continue

                        # 8.3 执行关注操作
                        if await self.execute_follow(follower['button_pos']):
                            # 8.4 关注成功，更新统计数据
                            self.stats['total_followed'] += 1
                            # 确保延迟参数顺序正确，防止randrange错误
                            min_delay = min(self.follow_delay_min, self.follow_delay_max)
                            max_delay = max(self.follow_delay_min, self.follow_delay_max)
                            关注后随机延迟_ms = random.randint(min_delay, max_delay)
                            关注后随机延迟_秒 = 关注后随机延迟_ms / 1000.0  # 转换为秒

                            # 8.5 检查是否完成任务目标
                            if self.stats['total_followed'] >= self.fans_follow_count:
                                # 🎯 发送任务完成状态更新信号到UI
                                self._emit_progress_update()
                                return f"已完成任务，共关注 {self.stats['total_followed']} 人"

                            log_info(f"[模拟器{self.emulator_id}] 已关注数：{self.stats['total_followed']}/{self.fans_follow_count}, {关注后随机延迟_ms}毫秒后下一个, 耗时: {time.time() - start_time:.2f} 秒",
                                    component="InstagramFollowTask")

                            # 🎯 发送状态更新信号到UI
                            self._emit_progress_update()

                            await asyncio.sleep(关注后随机延迟_秒)
                    else:
                        # 8.6 用户不符合筛选条件，跳过（不打印日志）
                        pass

                # 🎯 步骤9：滚动粉丝列表（使用预计算参数）
                scroll_params = ui_data.get("scroll_params", {})
                if scroll_params:
                    await self._scroll_with_precomputed_params(scroll_params)
                else:
                    # 降级到原有滚动方式
                    await self._scroll_followers_list()
                log_info(f"[模拟器{self.emulator_id}] 滚动完成，开始等待1秒让页面稳定加载", component="InstagramFollowTask")
                await asyncio.sleep(1)  # 改为1秒
                log_info(f"[模拟器{self.emulator_id}] 1秒等待完成，准备下一轮循环", component="InstagramFollowTask")

            return ""

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 处理粉丝列表异常: {e}", component="InstagramFollowTask")
            return "异常"

    async def _batch_detect_all_ui_elements(self):
        """批量检测所有UI元素（基于私信任务的成熟实现）"""
        try:
            import time
            import xml.etree.ElementTree as ET
            import re

            start_time = time.time()
            log_info(f"[模拟器{self.emulator_id}] 开始检测粉丝列表", component="InstagramFollowTask")

            # 第1步: 一次性获取UI结构（使用多语言支持方法，带重试机制）
            xml_content = None
            for ui_attempt in range(3):  # 最多重试3次获取UI
                success, xml_content = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui.xml")

                if success and xml_content and len(xml_content.strip()) > 100:
                    # 获取成功且内容有效
                    break
                else:
                    if ui_attempt < 2:  # 不是最后一次尝试
                        log_warning(f"[模拟器{self.emulator_id}] 第{ui_attempt + 1}次检测失败，重试中", component="InstagramFollowTask")
                        await asyncio.sleep(1)
                    else:
                        log_error(f"[模拟器{self.emulator_id}] 检测失败，已重试3次", component="InstagramFollowTask")
                        return None

            if not xml_content:
                log_error(f"[模拟器{self.emulator_id}] 最终检测失败", component="InstagramFollowTask")
                return None

            # 第2步: 解析XML并检测所有元素（带简单重试）
            root = None
            for parse_attempt in range(2):  # 最多尝试2次解析
                try:
                    root = ET.fromstring(xml_content)
                    break  # 解析成功，跳出循环
                except ET.ParseError as parse_error:
                    if parse_attempt == 0:
                        log_warning(f"[模拟器{self.emulator_id}] 页面解析失败，重新检测", component="InstagramFollowTask")
                        # 重新获取XML
                        await asyncio.sleep(0.5)
                        success, xml_content = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui.xml")
                        if not success or not xml_content:
                            log_error(f"[模拟器{self.emulator_id}] 重新获取页面信息失败", component="InstagramFollowTask")
                            return None
                    else:
                        log_error(f"[模拟器{self.emulator_id}] XML解析最终失败: {parse_error}", component="InstagramFollowTask")
                        return None

            if root is None:
                log_error(f"[模拟器{self.emulator_id}] 无法解析XML内容", component="InstagramFollowTask")
                return None

            # 第3步: 初始化检测结果
            result = {
                "retry_prompt": False,
                "review_prompt": False,
                "view_more_button": False,
                "reached_bottom": False,
                "followers": []
            }

            # 第4步: 一次性遍历检测状态信息
            for elem in root.iter():
                elem_text = elem.get('text', '').strip()
                elem_resource_id = elem.get('resource-id', '')

                # 检查频繁提示
                if elem_text == "请稍后重试":
                    result["retry_prompt"] = True
                elif elem_text == "请求待审核":
                    result["review_prompt"] = True
                elif elem_text == "查看更多":
                    result["view_more_button"] = True

                # 检查底部标识
                if elem_resource_id == "com.instagram.android:id/row_header_textview":
                    result["reached_bottom"] = True

            # 第5步: 提取粉丝信息（使用私信任务的成熟逻辑）
            result["followers"] = await self._extract_followers_from_xml(root)

            # 第6步: 预计算滚动参数（避免滚动时重新查找UI）
            result["scroll_params"] = self._calculate_scroll_params_from_followers(result["followers"])

            total_time = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] 粉丝列表检测完成，耗时{total_time:.2f}秒", component="InstagramFollowTask")

            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 批量检测异常: {e}", component="InstagramFollowTask")
            return None

    async def _extract_followers_from_xml(self, root):
        """从XML中提取粉丝信息（完全基于私信任务的成熟实现）"""
        try:
            import re
            followers = []

            # 查找所有粉丝容器（与私信任务相同）
            containers = []
            for elem in root.iter():
                if elem.get('resource-id') == "com.instagram.android:id/follow_list_container":
                    containers.append(elem)

            log_info(f"[模拟器{self.emulator_id}] 找到{len(containers)}个粉丝栏", component="InstagramFollowTask")

            # 批量处理每个容器（完全采用私信任务的逻辑）
            for container in containers:
                # 初始化容器内的元素变量
                username_elem = None
                follow_button_elem = None

                # 获取容器坐标范围（与私信任务相同）
                container_bounds = container.get('bounds', '')
                if not container_bounds:
                    continue

                container_coords = re.findall(r'\d+', container_bounds)
                if len(container_coords) < 4:
                    continue

                cx1, cy1, cx2, cy2 = map(int, container_coords[:4])

                # 在容器范围内查找子元素（与私信任务完全相同的逻辑）
                for child in root.iter():
                    child_bounds = child.get('bounds', '')
                    if not child_bounds:
                        continue

                    child_coords = re.findall(r'\d+', child_bounds)
                    if len(child_coords) < 4:
                        continue

                    chx1, chy1, chx2, chy2 = map(int, child_coords[:4])

                    # 检查是否在容器范围内（与私信任务相同的范围检查）
                    if chx1 >= cx1 and chy1 >= cy1 and chx2 <= cx2 and chy2 <= cy2:
                        # 查找用户名节点
                        if child.get('resource-id') == "com.instagram.android:id/follow_list_subtitle":
                            username_elem = child

                        # 查找关注按钮节点
                        elif child.get('resource-id') == "com.instagram.android:id/follow_list_row_large_follow_button":
                            follow_button_elem = child

                # 处理找到的元素（适配关注任务的需求）
                if username_elem is not None:
                    username = username_elem.get('text', '').strip()
                    if username:
                        # 计算按钮位置和关注状态
                        button_pos = None
                        is_followed = True  # 默认已关注

                        if follow_button_elem is not None:
                            button_text = follow_button_elem.get('text', '').strip()
                            # 解析按钮位置坐标
                            button_bounds = follow_button_elem.get('bounds', '')
                            button_coords = re.findall(r'\d+', button_bounds)
                            if len(button_coords) >= 4:
                                button_x = (int(button_coords[0]) + int(button_coords[2])) // 2
                                button_y = (int(button_coords[1]) + int(button_coords[3])) // 2
                                button_pos = (button_x, button_y)

                            # 判断关注状态：关注=未关注状态，已关注=已关注状态
                            is_followed = button_text in ["已关注", "已请求"]

                        # 构建粉丝信息字典
                        followers.append({
                            'nickname': username,
                            'button_pos': button_pos,
                            'is_followed': is_followed
                        })

            log_info(f"[模拟器{self.emulator_id}] 成功提取 {len(followers)} 个粉丝信息", component="InstagramFollowTask")
            return followers

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 提取粉丝信息异常: {e}", component="InstagramFollowTask")
            return []

    def _calculate_scroll_params_from_followers(self, followers):
        """基于粉丝数量预计算滚动参数（避免滚动时重新查找UI）"""
        try:
            # 使用粉丝数量计算滚动参数
            visible_count = len(followers) if followers else 4  # 默认4个
            scroll_items = max(1, visible_count)  # 滚动位置数等于可见粉丝数

            # 获取屏幕尺寸
            width, height = self.ld.get_screen_size(self.emulator_id)

            # 计算滚动距离
            item_height = int(height * 0.7) // visible_count if visible_count > 0 else 100
            item_height = max(50, min(item_height, 150))  # 限制在合理范围内
            scroll_distance = item_height * scroll_items

            # 计算滚动坐标
            center_x = width // 2
            start_y = int(height * 0.75)
            end_y = max(start_y - scroll_distance, int(height * 0.2))

            scroll_params = {
                "visible_count": visible_count,
                "scroll_items": scroll_items,
                "scroll_distance": scroll_distance,
                "center_x": center_x,
                "start_y": start_y,
                "end_y": end_y
            }

            # 预计算滚动参数，不打印日志
            return scroll_params

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 计算滚动参数异常: {e}", component="InstagramFollowTask")
            return {}

    async def _scroll_with_precomputed_params(self, scroll_params):
        """使用预计算参数进行滚动（避免重新查找UI元素）"""
        try:
            import time
            start_time = time.time()

            log_info(f"[模拟器{self.emulator_id}] 开始滑动", component="InstagramFollowTask")

            # 使用预计算的参数执行滑动
            swipe_result = self.ld.swipe(
                self.emulator_id,
                scroll_params["center_x"],
                scroll_params["start_y"],
                scroll_params["center_x"],
                scroll_params["end_y"],
                500  # 滑动持续时间
            )

            total_time = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] 滚动完成，耗时{total_time:.2f}秒", component="InstagramFollowTask")

            return bool(swipe_result)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 预计算滚动异常: {e}，回退到原有滚动方式", component="InstagramFollowTask")
            return await self._scroll_followers_list()

# ============================================================================
# 🎯 6. 辅助方法系统（完全按照参考代码实现）
# ============================================================================
# 功能描述: Instagram关注任务的辅助方法集合，提供通用的操作功能
# 调用关系: 被核心关注方法调用，实现具体的UI操作和状态检测
# 注意事项: 完全按照参考代码的逻辑实现，确保行为一致性
# 技术实现: 包含页面跳转、状态检测、坐标点击等基础操作方法
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 6.1 用户名获取步骤（边读边删机制）
    # ------------------------------------------------------------------------

    def _get_next_follow_user(self) -> str:
        """线程安全获取并删除用户名（参考代码一致的逻辑）"""
        try:
            import os
            import threading

            # 使用线程锁确保线程安全
            if not hasattr(self, '_user_lock'):
                self._user_lock = threading.Lock()

            with self._user_lock:
                # 🎯 步骤1：加载用户数据
                user_data = self._load_follow_users()
                if not user_data:
                    log_info(f"[模拟器{self.emulator_id}] 用户列表已耗尽", component="InstagramFollowTask")
                    return None

                # 🎯 步骤2：获取第一个用户并从列表中移除
                username = user_data.pop(0)

                # 🎯 步骤3：立即保存剩余数据
                self._save_follow_users(user_data)

                return username

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 获取用户名异常: {e}", component="InstagramFollowTask")
            return None

    def _load_follow_users(self) -> list:
        """加载关注用户列表"""
        try:
            import os
            if os.path.exists(self.follow_users_path):
                with open(self.follow_users_path, "r", encoding="utf-8") as f:
                    return [line.strip() for line in f if line.strip()]
            return []
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载用户列表失败: {e}", component="InstagramFollowTask")
            return []

    def _save_follow_users(self, user_data: list):
        """保存剩余用户数据到文件"""
        try:
            with open(self.follow_users_path, "w", encoding="utf-8") as f:
                f.write("\n".join(user_data))
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 保存用户列表失败: {e}", component="InstagramFollowTask")

    async def _step_open_followers_list(self) -> bool:
        """打开粉丝列表页面（参考代码一致的逻辑）"""
        try:
            # 🎯 步骤1：查找粉丝数控件（添加重试机制）
            followers_node = None
            for retry_attempt in range(3):  # 最多重试3次
                followers_node = self.ld.find_node(
                    resource_id="com.instagram.android:id/row_profile_header_textview_followers_count"
                )

                if followers_node and followers_node.get('bounds'):
                    # 找到有效的粉丝数控件
                    break
                else:
                    if retry_attempt < 2:  # 不是最后一次尝试
                        log_warning(f"[模拟器{self.emulator_id}] 第{retry_attempt + 1}次未找到粉丝数控件，1秒后重试", component="InstagramFollowTask")
                        await asyncio.sleep(1)
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] 未找到粉丝数控件，已重试3次", component="InstagramFollowTask")
                        return False

            # 🎯 步骤2：点击粉丝数控件（修复解包错误）
            click_result = self.ld.click_node(followers_node)
            if not click_result:
                log_error(f"[模拟器{self.emulator_id}] 点击粉丝数控件失败", component="InstagramFollowTask")
                return False

            # 🎯 步骤3：等待粉丝列表页面加载
            try:
                result = self.ld.wait_for(
                    resource_id="com.instagram.android:id/title",
                    timeout=20,
                    interval=1
                )

                # 🎯 步骤4：检查是否成功进入粉丝列表
                if result:
                    # 检查是否有"找不到任何用户"的提示
                    if not self.ld.find_node(resource_id="com.instagram.android:id/row_no_results_textview"):
                        log_info(f"[模拟器{self.emulator_id}] 成功打开粉丝列表", component="InstagramFollowTask")
                        return True
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] 粉丝列表为空", component="InstagramFollowTask")
                        return False
                else:
                    log_error(f"[模拟器{self.emulator_id}] 粉丝列表页面加载超时", component="InstagramFollowTask")
                    return False

            except Exception as e:
                log_error(f"[模拟器{self.emulator_id}] 等待粉丝列表页面异常: {e}", component="InstagramFollowTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 打开粉丝列表异常: {e}", component="InstagramFollowTask")
            return False

    async def _scroll_followers_list(self) -> bool:
        """滚动粉丝列表（复制自私信任务的智能滚动方式）"""
        try:
            import time
            start_time = time.time()
            log_info(f"[模拟器{self.emulator_id}] 开始滚动粉丝列表 - 第1步：检测当前屏幕粉丝数量", component="InstagramFollowTask")

            # 智能滑动：检测容器数量，滑动等于可见粉丝数的位置
            detect_start = time.time()
            current_followers = self.ld.find_nodes(resource_id="com.instagram.android:id/follow_list_container")
            visible_count = len(current_followers) if current_followers else 4  # 默认4个
            scroll_items = max(1, visible_count)  # 滚动位置数等于可见粉丝数
            detect_time = time.time() - detect_start
            log_info(f"[模拟器{self.emulator_id}] 第1步完成：检测到{visible_count}个可见粉丝，耗时{detect_time:.2f}秒", component="InstagramFollowTask")

            # 计算滑动距离
            calc_start = time.time()
            width, height = self.ld.get_screen_size(self.emulator_id)
            item_height = int(height * 0.7) // visible_count  # 简单估算每项高度
            item_height = max(50, min(item_height, 150))  # 限制在合理范围
            scroll_distance = item_height * scroll_items

            # 执行滑动
            center_x = width // 2
            start_y = int(height * 0.75)
            end_y = max(start_y - scroll_distance, int(height * 0.2))
            calc_time = time.time() - calc_start
            log_info(f"[模拟器{self.emulator_id}] 第2步完成：计算滑动参数，滑动{scroll_items}个位置，距离{scroll_distance}px，耗时{calc_time:.2f}秒", component="InstagramFollowTask")

            # 执行滑动操作
            swipe_start = time.time()
            log_info(f"[模拟器{self.emulator_id}] 第3步：开始执行滑动操作（500毫秒滑动时间）", component="InstagramFollowTask")
            swipe_result = self.ld.swipe(self.emulator_id, center_x, start_y, center_x, end_y, 500)
            swipe_time = time.time() - swipe_start
            log_info(f"[模拟器{self.emulator_id}] 第3步完成：滑动操作完成，耗时{swipe_time:.2f}秒", component="InstagramFollowTask")

            total_time = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] 滚动粉丝列表总耗时：{total_time:.2f}秒", component="InstagramFollowTask")

            return bool(swipe_result)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 滚动粉丝列表异常: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.2 页面导航步骤
    # ------------------------------------------------------------------------
    # 功能描述: 跳转到指定用户的Instagram资料页面
    # 调用关系: 被关注方法调用，作为关注操作的第一步
    # 注意事项: 使用Instagram deep link机制，支持直接跳转
    # 技术实现: 通过ADB命令发送Intent，等待页面加载完成
    # ------------------------------------------------------------------------

    async def navigate_to_profile(self, user_id: str) -> bool:
        """🎯 跳转到指定用户资料页（参考代码一致的逻辑）"""
        try:
            import time
            log_info(f"[模拟器{self.emulator_id}] 跳转到用户主页：{user_id}", component="InstagramFollowTask")
            start_time = time.time()

            # 🎯 步骤1：构造并执行Instagram deep link命令
            cmd = f'am start -a android.intent.action.VIEW -d "instagram://user?username={user_id}"'

            # 使用修复后的ADB命令执行方式（关键修复）
            success, output = self.ld.execute_ld(self.emulator_id, cmd, silence=False)

            # 检查命令是否真正执行成功
            if not success or (output and ("not found" in output or "error" in output.lower())):
                log_error(f"[模拟器{self.emulator_id}] ADB命令执行失败: {output}", component="InstagramFollowTask")
                return False

            # ADB命令执行成功，不打印日志

            # 🎯 步骤2：等待页面加载并验证
            timeout_threshold = 30  # 用户资料页加载超时（参考代码一致）
            while time.time() - start_time < timeout_threshold:
                # 检查是否已在用户资料页
                if await self.check_current_profile(user_id):
                    log_info(f"[模拟器{self.emulator_id}] 当前已在用户主页：{user_id}", component="InstagramFollowTask")
                    return True
                await asyncio.sleep(0.5)

            log_error(f"[模拟器{self.emulator_id}] 跳转到用户主页超时：{user_id}", component="InstagramFollowTask")
            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 跳转到用户主页异常：{e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.3 用户资料页验证步骤
    # ------------------------------------------------------------------------

    async def check_current_profile(self, user_id: str) -> bool:
        """检查当前是否在指定用户的资料页"""
        try:
            # 🎯 步骤1：优先检查标题栏用户名（最准确的验证方式）
            title_element = self.ld.find_node(resource_id="com.instagram.android:id/action_bar_title")
            if title_element:
                title_text = title_element.get('text', '')
                if title_text == user_id:
                    log_info(f"[模拟器{self.emulator_id}] ✅ 标题栏验证成功，当前在用户 {user_id} 的资料页", component="InstagramFollowTask")
                    return True

            # 🎯 步骤2：检查用户资料页的关键元素
            profile_elements = [
                "com.instagram.android:id/row_profile_header_textview_followers_count",
                "com.instagram.android:id/profile_header_follow_button"
            ]

            # 🎯 步骤3：检查是否有足够的资料页元素
            found_elements = 0
            for element_id in profile_elements:
                element = self.ld.find_node(resource_id=element_id)
                if element:
                    found_elements += 1

            # 如果找到2个或以上元素，认为在资料页
            if found_elements >= 2:
                log_info(f"[模拟器{self.emulator_id}] ✅ 检测到资料页元素，可能在用户资料页", component="InstagramFollowTask")
                return True

            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查用户资料页异常：{e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.4 账户状态检测步骤
    # ------------------------------------------------------------------------

    async def skip_special_account(self) -> tuple[bool, str]:
        """综合检测账户状态（私密/蓝V/粉丝数）（参考代码一致的逻辑）"""
        try:
            # 🎯 步骤1：检测私密账户和蓝V
            is_private = await self.is_private_account()
            is_blue = await self.is_blue_verified()

            # 🎯 步骤2：更新统计计数器并确定具体类型
            skip_reason = ""
            if is_private and is_blue:
                log_info(f"[模拟器{self.emulator_id}] 检测到私密且蓝V认证账户", component="InstagramFollowTask")
                self.stats['skipped_private'] += 1
                self.stats['skipped_verified'] += 1
                skip_reason = "私密且蓝V账户"
            elif is_private:
                log_info(f"[模拟器{self.emulator_id}] 检测到私密账户", component="InstagramFollowTask")
                self.stats['skipped_private'] += 1
                skip_reason = "私密账户"
            elif is_blue:
                log_info(f"[模拟器{self.emulator_id}] 检测到蓝V认证账户", component="InstagramFollowTask")
                self.stats['skipped_verified'] += 1
                skip_reason = "蓝V认证账户"

            # 🎯 步骤3：返回是否需要跳过和具体原因
            should_skip = is_private or is_blue
            return should_skip, skip_reason

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检测特殊账户异常：{e}", component="InstagramFollowTask")
            return True, f"检测异常: {e}"

    async def is_private_account(self) -> bool:
        """检测是否为私密账户（使用包含匹配）"""
        try:
            # 🎯 步骤1：定义私密账户关键词（包含"账户"和"帐户"两种写法）
            private_keywords = [
                "私密账户", "私密账号", "私人帐户", "此帐户为私人帐户", "这是私密账户",
                "私密帐户", "私密帐号", "私人账户", "此账户为私人账户", "这是私密帐户",
                "Private", "private", "This Account is Private"
            ]

            # 🎯 步骤2：获取页面所有文本元素进行包含匹配
            import xml.etree.ElementTree as ET
            success, xml_content = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui_private_check.xml")

            if success and xml_content:
                root = ET.fromstring(xml_content)
                for elem in root.iter():
                    elem_text = elem.get('text', '').strip()
                    if elem_text and any(keyword in elem_text for keyword in private_keywords):
                        log_info(f"[模拟器{self.emulator_id}] 🔍 检测到私密账户标识: '{elem_text}'", component="InstagramFollowTask")
                        return True

            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检测私密账户异常：{e}", component="InstagramFollowTask")
            return False

    async def is_blue_verified(self) -> bool:
        """检测是否为蓝V认证账户（参考代码一致的逻辑）"""
        try:
            # 🎯 步骤1：使用参考代码的正确resource_id查找蓝V认证标识
            verified_element = self.ld.find_node(
                resource_id="com.instagram.android:id/action_bar_title_verified_badge"
            )

            # 🎯 步骤2：添加详细的调试信息
            if verified_element:
                log_info(f"[模拟器{self.emulator_id}] 🔍 蓝V检测: 找到认证标识", component="InstagramFollowTask")
                # 认证元素详情，不打印日志
                return True
            else:
                log_info(f"[模拟器{self.emulator_id}] 🔍 蓝V检测: 未找到认证标识元素", component="InstagramFollowTask")

                # 🎯 步骤3：备用检测方法（检查其他可能的蓝V标识）
                backup_verified_element = self.ld.find_node(
                    resource_id="com.instagram.android:id/row_profile_header_textview_verified_badge"
                )
                if backup_verified_element:
                    log_info(f"[模拟器{self.emulator_id}] 🔍 蓝V检测: 备用方法找到认证标识", component="InstagramFollowTask")
                    return True

                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检测蓝V认证异常：{e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.5 粉丝数检查步骤
    # ------------------------------------------------------------------------

    async def check_followers_count(self) -> bool:
        """检查粉丝数（参考代码一致的逻辑）"""
        try:
            # 🎯 步骤1：检查粉丝数是否符合要求
            # 这里可以添加粉丝数检查逻辑
            # 暂时返回True，表示粉丝数符合要求
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查粉丝数异常：{e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.25 直接关注专用批量检测步骤
    # ------------------------------------------------------------------------
    # 功能描述: 专门为直接关注任务设计的批量UI检测方法，一次性获取所有用户资料信息
    # 调用关系: 仅被直接关注任务的follow_user()方法调用，不影响粉丝关注任务
    # 注意事项: 使用独立的XML文件名，避免与粉丝关注任务的批量检测冲突
    # 技术实现: 基于粉丝关注的_batch_detect_all_ui_elements架构，专用于用户资料页面
    # ------------------------------------------------------------------------

    async def _direct_follow_batch_detect(self, expected_user_id: str) -> dict:
        """🎯 直接关注专用：批量检测用户资料页面所有UI元素"""
        try:
            import time
            import xml.etree.ElementTree as ET
            import re
            import asyncio

            start_time = time.time()
            log_info(f"[模拟器{self.emulator_id}] 开始检测用户信息", component="InstagramFollowTask")

            # 第1步: 一次性获取UI结构（使用多语言支持方法，带重试机制）
            xml_content = None
            for ui_attempt in range(6):  # 最多重试6次获取UI（6秒内）
                success, xml_content = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui_direct_follow.xml")

                if success and xml_content and len(xml_content.strip()) > 100:
                    # 获取成功且内容有效
                    break
                else:
                    if ui_attempt < 5:  # 不是最后一次尝试
                        await asyncio.sleep(1)  # 等待1秒后重试
                    else:
                        log_error(f"[模拟器{self.emulator_id}] 用户信息检测失败，已重试6次", component="InstagramFollowTask")
                        return None

            if not xml_content:
                log_error(f"[模拟器{self.emulator_id}] 最终用户信息检测失败", component="InstagramFollowTask")
                return None

            # 第2步: 解析XML并检测所有元素（带简单重试）
            root = None
            for parse_attempt in range(2):  # 最多尝试2次解析
                try:
                    root = ET.fromstring(xml_content)
                    break  # 解析成功，跳出循环
                except ET.ParseError as parse_error:
                    if parse_attempt == 0:
                        log_warning(f"[模拟器{self.emulator_id}] 页面解析失败，重新检测", component="InstagramFollowTask")
                        # 重新获取XML
                        await asyncio.sleep(0.5)
                        success, xml_content = self.ld.execute_ld_with_multilingual_support(self.emulator_id, "uiautomator dump /sdcard/ui_direct_follow.xml")
                        if not success or not xml_content:
                            log_error(f"[模拟器{self.emulator_id}] 重新获取页面信息失败", component="InstagramFollowTask")
                            return None
                    else:
                        log_error(f"[模拟器{self.emulator_id}] 页面解析最终失败", component="InstagramFollowTask")
                        return None

            if root is None:
                log_error(f"[模拟器{self.emulator_id}] 无法解析页面内容", component="InstagramFollowTask")
                return None

            # 第3步: 初始化检测结果
            result = {
                "page_loaded": False,
                "correct_user": False,
                "is_private": False,
                "is_verified": False,
                "is_followed": False,
                "follow_button_pos": None,
                "title_verified": False,
                "followers_count": None,
                "following_count": None
            }

            # 第4步: 一次性遍历检测用户资料页面所有信息
            profile_elements_found = 0

            for elem in root.iter():
                elem_text = elem.get('text', '').strip()
                elem_resource_id = elem.get('resource-id', '')

                # 🎯 检测标题栏用户名（验证是否在正确页面）
                if elem_resource_id == "com.instagram.android:id/action_bar_title" and elem_text == expected_user_id:
                    result["correct_user"] = True
                    result["title_verified"] = True
                    profile_elements_found += 1
                    log_info(f"[模拟器{self.emulator_id}] ✅ 标题栏验证成功，当前在用户 {expected_user_id} 的资料页", component="InstagramFollowTask")

                # 🎯 检测私密账户（使用包含匹配）
                private_keywords = ["私密账户", "私密账号", "私人帐户", "Private"]
                if any(keyword in elem_text for keyword in private_keywords):
                    result["is_private"] = True
                    profile_elements_found += 1
                    log_info(f"[模拟器{self.emulator_id}] 🔍 私密账户检测: 找到私密标识 '{elem_text}'", component="InstagramFollowTask")

                # 🎯 检测蓝V认证
                if elem_resource_id in ['com.instagram.android:id/action_bar_title_verified_badge',
                                      'com.instagram.android:id/row_profile_header_textview_verified_badge']:
                    result["is_verified"] = True
                    profile_elements_found += 1
                    log_info(f"[模拟器{self.emulator_id}] 🔍 蓝V检测: 找到认证标识", component="InstagramFollowTask")

                # 🎯 检测关注按钮状态和位置
                if elem_resource_id == 'com.instagram.android:id/profile_header_follow_button':
                    button_text = elem_text
                    result["is_followed"] = button_text in ["已关注", "已请求"]

                    # 获取按钮位置坐标
                    bounds = elem.get('bounds', '')
                    coords = re.findall(r'\d+', bounds)

                    if len(coords) >= 4:
                        button_x = (int(coords[0]) + int(coords[2])) // 2
                        button_y = (int(coords[1]) + int(coords[3])) // 2
                        result["follow_button_pos"] = (button_x, button_y)

                    profile_elements_found += 1
                    log_info(f"[模拟器{self.emulator_id}] 🔍 关注状态检测: {button_text}", component="InstagramFollowTask")

                # 🎯 获取粉丝数信息
                if elem_resource_id == 'com.instagram.android:id/row_profile_header_textview_followers_count':
                    result["followers_count"] = elem_text
                    profile_elements_found += 1

                # 🎯 获取关注数信息
                if elem_resource_id == 'com.instagram.android:id/row_profile_header_textview_following_count':
                    result["following_count"] = elem_text
                    profile_elements_found += 1

            # 第5步: 判断页面是否加载完成
            result["page_loaded"] = profile_elements_found > 0 and result["correct_user"]

            total_time = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] 用户信息检测完成，耗时{total_time:.2f}秒", component="InstagramFollowTask")

            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 用户信息检测异常: {e}", component="InstagramFollowTask")
            return None

    # ------------------------------------------------------------------------
    # 🎯 6.6 粉丝信息获取步骤
    # ------------------------------------------------------------------------

    async def get_visible_followers(self) -> list:
        """获取当前屏幕可见的粉丝信息列表（高效批量解析方式）"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始高效批量获取当前屏幕可见的粉丝列表", component="InstagramFollowTask")

            followers = []

            # 🎯 第1步: 一次性获取UI结构（高效方式，带重试机制）
            xml_content = None
            max_retries = 3  # 最多重试3次

            for attempt in range(max_retries):
                try:
                    # 尝试获取UI结构（使用多语言支持方法）
                    success, xml_content = self.ld.execute_ld_with_korean_support(self.emulator_id, "uiautomator dump /sdcard/ui.xml")
                    if not success or not xml_content:
                        if attempt < max_retries - 1:
                            log_warning(f"[模拟器{self.emulator_id}] 获取页面信息失败，重试 {attempt + 1}/{max_retries}", component="InstagramFollowTask")
                            await asyncio.sleep(1)  # 等待1秒后重试
                            continue
                        else:
                            log_error(f"[模拟器{self.emulator_id}] 获取页面信息失败，已重试{max_retries}次", component="InstagramFollowTask")
                            return []

                    # 成功获取XML内容，跳出重试循环
                    log_info(f"[模拟器{self.emulator_id}] UI结构获取成功{f'（第{attempt + 1}次尝试）' if attempt > 0 else ''}", component="InstagramFollowTask")
                    break

                except Exception as e:
                    if attempt < max_retries - 1:
                        log_warning(f"[模拟器{self.emulator_id}] UI获取异常，重试 {attempt + 1}/{max_retries}: {e}", component="InstagramFollowTask")
                        await asyncio.sleep(1)
                        continue
                    else:
                        log_error(f"[模拟器{self.emulator_id}] UI获取异常，已重试{max_retries}次: {e}", component="InstagramFollowTask")
                        return []

            # 🎯 第2步: 批量解析XML查找所有粉丝容器节点
            import xml.etree.ElementTree as ET
            import re

            try:
                root = ET.fromstring(xml_content)

                # 查找所有粉丝容器
                containers = []
                for elem in root.iter():
                    if elem.get('resource-id') == "com.instagram.android:id/follow_list_container":
                        containers.append(elem)

                log_info(f"[模拟器{self.emulator_id}] 找到{len(containers)}个粉丝栏", component="InstagramFollowTask")

                # 🎯 第3步: 批量处理每个容器，在容器范围内查找子元素
                for container in containers:
                    # 初始化容器内的元素变量
                    username_elem = None
                    follow_button_elem = None

                    # 获取容器坐标范围
                    container_bounds = container.get('bounds', '')
                    if not container_bounds:
                        continue

                    container_coords = re.findall(r'\d+', container_bounds)
                    if len(container_coords) < 4:
                        continue

                    cx1, cy1, cx2, cy2 = map(int, container_coords[:4])

                    # 在容器范围内查找子元素
                    for child in root.iter():
                        child_bounds = child.get('bounds', '')
                        if not child_bounds:
                            continue

                        child_coords = re.findall(r'\d+', child_bounds)
                        if len(child_coords) < 4:
                            continue

                        chx1, chy1, chx2, chy2 = map(int, child_coords[:4])

                        # 检查是否在容器范围内
                        if chx1 >= cx1 and chy1 >= cy1 and chx2 <= cx2 and chy2 <= cy2:
                            # 查找用户名节点
                            if child.get('resource-id') == "com.instagram.android:id/follow_list_subtitle":
                                username_elem = child

                            # 查找关注按钮节点
                            elif child.get('resource-id') == "com.instagram.android:id/follow_list_row_large_follow_button":
                                follow_button_elem = child

                    # 🎯 第4步: 构建粉丝信息对象
                    if username_elem is not None and follow_button_elem is not None:
                        # 获取用户名
                        username = username_elem.get('text', '').strip()

                        # 获取按钮状态
                        button_text = follow_button_elem.get('text', '').strip()
                        is_followed = button_text in ['已关注', '已请求']

                        # 解析按钮位置坐标
                        button_bounds = follow_button_elem.get('bounds', '')
                        button_coords = re.findall(r'\d+', button_bounds)
                        if len(button_coords) >= 4:
                            x1, y1, x2, y2 = map(int, button_coords[:4])
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            button_pos = (center_x, center_y)
                        else:
                            button_pos = None

                        if username and button_pos:
                            followers.append({
                                "nickname": username,
                                "button_pos": button_pos,
                                "is_followed": is_followed
                            })

                log_info(f"[模拟器{self.emulator_id}] 当前屏幕发现 {len(followers)} 个可操作粉丝", component="InstagramFollowTask")
                return followers

            except ET.ParseError as e:
                log_error(f"[模拟器{self.emulator_id}] 页面解析失败: {e}", component="InstagramFollowTask")
                return []

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 获取可见粉丝异常: {e}", component="InstagramFollowTask")
            return []

    # ------------------------------------------------------------------------
    # 🎯 6.7 用户筛选步骤
    # ------------------------------------------------------------------------

    async def should_follow(self, nickname: str) -> bool:
        """根据昵称和地区列表判断是否关注（参考代码一致的逻辑）"""
        try:
            # 🎯 步骤1：构建目标地区列表（根据实际配置参数）
            target_regions = []

            # 根据实际配置构建地区列表
            if self.all_regions:
                target_regions.append('所有地区')  # 对应UI中的"所有地区"选项
            if self.japan:
                target_regions.append('日本')
            if self.korea:
                target_regions.append('韩国')
            if self.thailand:
                target_regions.append('泰国')

            # 如果没有选择任何地区，默认为所有地区
            if not target_regions:
                target_regions = ['所有地区']

            # 当前地区筛选配置，不打印日志

            # 🎯 步骤2：执行地区筛选逻辑（完全按照参考代码）
            return self._check_nickname_region(nickname, target_regions)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查用户筛选条件异常: {e}", component="InstagramFollowTask")
            return False

    def _check_nickname_region(self, nickname: str, target_regions: list) -> bool:
        """检查昵称是否符合目标地区（参考代码一致的逻辑）"""
        try:
            import re

            nickname = nickname.lower().strip()
            if not nickname:
                return False

            # 🎯 语言特征正则表达式（根据我们支持的地区）
            jp_re = re.compile(r'[\u3040-\u309f\u30a0-\u30ff]')  # 日文假名
            kr_re = re.compile(r'[\uac00-\ud7af]')  # 韩文
            th_re = re.compile(r'[\u0e00-\u0e7f]')  # 泰文
            en_re = re.compile(r'^[a-z0-9_\s]+$')  # 英文/数字/空格

            # 🎯 注意：已移除问号占位符检测逻辑
            # 现在使用多语言支持方法，可以直接识别真实的韩文字符，无需依赖问号推测

            # 🎯 检测语言特征（只检测我们支持的地区）
            detected_language = "未知"
            result = False

            if kr_re.search(nickname):
                detected_language = "韩文"
                result = any(r in ['所有地区', '韩国'] for r in target_regions)
            elif jp_re.search(nickname):
                detected_language = "日文"
                result = any(r in ['所有地区', '日本'] for r in target_regions)
            elif th_re.search(nickname):
                detected_language = "泰文"
                result = any(r in ['所有地区', '泰国'] for r in target_regions)
            elif en_re.match(nickname):
                detected_language = "英文"
                result = '所有地区' in target_regions
            else:
                detected_language = "其他"
                result = '所有地区' in target_regions

            # 只在筛选结果为True时打印日志
            if result:
                log_info(f"[模拟器{self.emulator_id}] 🔍 语言检测: {detected_language}, 筛选结果: {result}", component="InstagramFollowTask")
            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 地区检查异常: {e}", component="InstagramFollowTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 6.8 关注操作执行步骤
    # ------------------------------------------------------------------------

    async def execute_follow(self, button_pos: tuple) -> bool:
        """通过按钮坐标执行关注操作（简化验证逻辑）"""
        try:
            # 🎯 步骤1：验证按钮坐标
            if not button_pos:
                log_warning(f"[模拟器{self.emulator_id}] 未找到关注按钮坐标", component="InstagramFollowTask")
                return False

            # 🎯 步骤2：执行点击操作（添加详细调试，确认API返回值）
            x, y = button_pos
            try:
                # 执行点击并获取返回值
                result = self.ld.click(x, y)

                # 添加详细调试日志
                log_info(f"[模拟器{self.emulator_id}] 点击API返回值：{result}，类型：{type(result)}", component="InstagramFollowTask")

                if isinstance(result, tuple) and len(result) >= 2:
                    # 如果返回元组 (success, cost_time)
                    success, cost_time = result
                    if success:
                        log_info(f"[模拟器{self.emulator_id}] 关注成功，坐标：({x}, {y})，耗时{cost_time:.2f}秒", component="InstagramFollowTask")
                        await asyncio.sleep(0.1)  # 等待关注状态更新，似乎不生效，被关注间隔所覆盖
                        return True
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] 点击失败，坐标：({x}, {y})，API返回success=False", component="InstagramFollowTask")
                        return False
                elif isinstance(result, str):
                    # 如果返回字符串类型
                    if result == "" or result.lower() in ["success", "ok", "true"]:
                        log_info(f"[模拟器{self.emulator_id}] 关注成功，坐标：({x}, {y})，API返回字符串：'{result}'", component="InstagramFollowTask")
                        await asyncio.sleep(0.1)  # 等待关注状态更新，，这个应该不会生效，应该会被
                        return True
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] 点击失败，坐标：({x}, {y})，API返回字符串：'{result}'", component="InstagramFollowTask")
                        return False
                else:
                    # 如果返回布尔值或其他类型
                    if result:
                        log_info(f"[模拟器{self.emulator_id}] 关注成功，坐标：({x}, {y})", component="InstagramFollowTask")
                        await asyncio.sleep(0.1)  # 等待关注状态更新，似乎不生效，被关注间隔所覆盖
                        return True
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] 点击失败，坐标：({x}, {y})，API返回{result}", component="InstagramFollowTask")
                        return False

            except Exception as click_e:
                log_error(f"[模拟器{self.emulator_id}] 点击操作异常：{click_e}", component="InstagramFollowTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 关注操作异常：{e}", component="InstagramFollowTask")
            return False


