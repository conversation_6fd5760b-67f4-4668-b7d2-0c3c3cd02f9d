2025-07-27 10:40:26 - root - INFO - 简化日志系统初始化完成
2025-07-27 10:40:26 - main - INFO - 应用程序启动
2025-07-27 10:40:26 - __main__ - INFO - Qt应用程序已创建
2025-07-27 10:40:26 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 10:40:26 - __main__ - INFO - 统一配置管理器已创建
2025-07-27 10:40:26 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 10:40:26 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 10:40:26 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 10:40:26 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 10:40:26 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 10:40:26 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 10:40:26 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 10:40:26 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 10:40:26 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 10:40:26 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 10:40:26 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-27 10:40:26 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 10:40:26 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-27 10:40:26 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-27 10:40:26 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-27 10:40:26 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-27 10:40:26 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-27 10:40:26 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-27 10:40:27 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-27 10:40:27 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-27 10:40:27 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-27 10:40:27 - __main__ - INFO - UI主窗口已创建
2025-07-27 10:40:27 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-27 10:40:27 - __main__ - INFO - 主窗口已显示
2025-07-27 10:40:27 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 10:40:27 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-27 10:40:27 - __main__ - INFO - UI层和业务层已连接
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 10:40:27 - __main__ - INFO - 启动Qt事件循环
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 10:40:27 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 10:40:27 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 10:40:27 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 10:40:27 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 10:40:27 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 10:40:27 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 10:40:27 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 10:40:27 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 10:40:27 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.23s | count: 1229
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 10:40:27 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 10:40:27 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 10:40:27 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 10:40:27 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 10:40:27 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 10:40:27 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 10:40:27 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 10:40:27 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 10:40:27 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 10:40:27 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.22s | count: 1229
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 10:40:27 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 10:40:27 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 10:40:27 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 10:40:28 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-27 10:40:28 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-27 10:40:28 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-27 10:40:28 - __main__ - INFO - 后台服务已启动
2025-07-27 10:40:28 - __main__ - INFO - 延迟启动服务完成
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_fans_task
2025-07-27 10:40:30 - MainWindowV2 - INFO - Instagram关注粉丝任务已启动，涉及2个模拟器
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram关注粉丝任务请求
2025-07-27 10:40:30 - MainWindowV2 - INFO - 用户启动Instagram关注粉丝任务，模拟器数量: 2
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 开始处理Instagram关注粉丝任务（线程池模式），模拟器数量: 2
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [1, 2]
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-27 10:40:30 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-27 10:40:30 - StartupManager - INFO - 启动调度器已启动
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-27 10:40:30 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 10:40:30 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-27 10:40:30 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: fans
2025-07-27 10:40:30 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-27 10:40:30 - InstagramFollowTaskThread - INFO - [模拟器1] Instagram关注任务线程初始化完成，模式: fans
2025-07-27 10:40:30 - InstagramFollowTaskManager - INFO - 为模拟器 1 创建Instagram关注任务线程，模式: fans
2025-07-27 10:40:30 - InstagramFollowTaskThread - INFO - [模拟器2] Instagram关注任务线程初始化完成，模式: fans
2025-07-27 10:40:30 - InstagramFollowTaskManager - INFO - 为模拟器 2 创建Instagram关注任务线程，模式: fans
2025-07-27 10:40:30 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-27 10:40:30 - InstagramTaskManager - INFO - 启动模拟器1的Instagram任务线程 - 当前并发: 1/1
2025-07-27 10:40:30 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步桥接器: Instagram关注粉丝任务请求已处理，状态: started
2025-07-27 10:40:30 - InstagramTaskThread - INFO - [模拟器1] 开始智能等待启动完成
2025-07-27 10:40:30 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-27 10:40:30 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 10:40:30 - InstagramTaskThread - INFO - [模拟器1] 排队阶段完成，等待时间: 0.0秒，开始启动阶段
2025-07-27 10:40:30 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-27 10:40:30 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 10:40:30 - InstagramTaskThread - INFO - [模拟器1] 开始启动阶段，超时时间: 25秒
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 10:40:30 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 1
2025-07-27 10:40:30 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-27 10:40:30 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-27 10:40:30 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_fans_task
2025-07-27 10:40:30 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 排队中 | new_state: 启动中
2025-07-27 10:40:30 - MainWindowV2 - INFO - 模拟器1: 排队中 -> 启动中
2025-07-27 10:40:30 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:40:30 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 10:40:30 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 1
2025-07-27 10:40:40 - Emulator - INFO - Android系统启动完成 | emulator_id: 1 | elapsed_time: 9.8秒
2025-07-27 10:40:40 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 启动中 -> 运行中
2025-07-27 10:40:40 - Emulator - INFO - 模拟器启动成功 | emulator_id: 1 | running_count: 1
2025-07-27 10:40:40 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 启动中 | new_state: 运行中
2025-07-27 10:40:40 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已添加到任务活动监控，失败计数: 0
2025-07-27 10:40:40 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务start
2025-07-27 10:40:40 - MainWindowV2 - INFO - 模拟器1启动成功
2025-07-27 10:40:40 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 10:40:40 - WindowArrangementManager - INFO - 模拟器1启动完成，立即触发窗口排列
2025-07-27 10:40:40 - MainWindowV2 - WARNING - 未找到模拟器1，无法更新状态
2025-07-27 10:40:40 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 10:40:40 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:40:40 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 10:40:40 - InstagramTaskThread - INFO - [模拟器1] 启动完成，启动阶段: 10.0秒，总时间: 10.0秒
2025-07-27 10:40:40 - InstagramTaskThread - INFO - [模拟器1] 开始窗口排列
2025-07-27 10:40:40 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中, PID: 17736
2025-07-27 10:40:40 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 10:40:42 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 10:40:42 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 10:40:42 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 10:40:46 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:40:46 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:40:46 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:40:46 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:40:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:40:48 - InstagramTaskThread - INFO - [模拟器1] 窗口排列完成
2025-07-27 10:40:48 - InstagramFollowTaskThread - INFO - [模拟器1] 开始执行Instagram关注粉丝任务
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 雷电模拟器API初始化成功
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 已设置ld.emulator_id = 1
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置热加载观察者已注册
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] Instagram私信任务执行器初始化完成
2025-07-27 10:40:49 - InstagramFollowTask - INFO - [模拟器1] Instagram关注任务配置加载完成
2025-07-27 10:40:49 - InstagramFollowTask - INFO - [模拟器1] Instagram关注任务执行器初始化完成
2025-07-27 10:40:49 - InstagramFollowTask - INFO - [模拟器1] 关注模式已设置为: fans
2025-07-27 10:40:49 - InstagramFollowTask - INFO - [模拟器1] 开始执行Instagram关注任务
2025-07-27 10:40:49 - InstagramFollowTask - WARNING - [模拟器1] 任务开始时间未由线程传递，在此设置
2025-07-27 10:40:49 - InstagramFollowTask - INFO - [模拟器1] 任务超时设置: 66秒，已运行: 0.00秒
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 模拟器Android系统运行正常，桌面稳定
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 开始检查应用安装状态
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 📊 应用安装状态检测结果:
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] ✅ 所有必要应用已安装
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] 开始启动V2Ray应用
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] V2Ray启动命令执行成功，等待应用加载
2025-07-27 10:40:49 - InstagramDMTask - INFO - [模拟器1] V2Ray应用启动结果: 成功
2025-07-27 10:40:52 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray应用启动成功
2025-07-27 10:40:52 - InstagramDMTask - INFO - [模拟器1] 开始检查V2Ray节点列表状态
2025-07-27 10:40:54 - InstagramDMTask - INFO - [模拟器1] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 10:40:54 - InstagramDMTask - INFO - [模拟器1] 开始连接V2Ray节点
2025-07-27 10:40:55 - InstagramDMTask - INFO - [模拟器1] 当前连接状态: 未连接
2025-07-27 10:40:55 - InstagramDMTask - INFO - [模拟器1] V2Ray节点未连接，开始连接
2025-07-27 10:40:56 - InstagramDMTask - INFO - [模拟器1] 已点击连接按钮，等待连接完成
2025-07-27 10:40:58 - InstagramDMTask - INFO - [模拟器1] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 10:40:58 - InstagramDMTask - INFO - [模拟器1] V2Ray节点连接成功
2025-07-27 10:40:58 - InstagramDMTask - INFO - [模拟器1] 开始测试V2Ray节点延迟
2025-07-27 10:40:58 - InstagramDMTask - INFO - [模拟器1] 开始V2Ray节点延迟测试
2025-07-27 10:40:59 - InstagramDMTask - INFO - [模拟器1] 当前测试状态: 已连接，点击测试连接
2025-07-27 10:40:59 - InstagramDMTask - INFO - [模拟器1] 点击开始延迟测试
2025-07-27 10:41:00 - InstagramDMTask - INFO - [模拟器1] 已点击测试按钮，等待测试结果
2025-07-27 10:41:01 - InstagramDMTask - INFO - [模拟器1] 测试状态监控 (1/30): 连接成功：延时 271 毫秒
2025-07-27 10:41:01 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray节点延迟测试成功: 连接成功：延时 271 毫秒
2025-07-27 10:41:01 - InstagramDMTask - INFO - [模拟器1] 等待5秒后进入下一阶段
2025-07-27 10:41:06 - InstagramDMTask - INFO - [模拟器1] 开始启动Instagram应用
2025-07-27 10:41:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:41:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:41:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:41:06 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:41:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:41:06 - InstagramDMTask - INFO - [模拟器1] Instagram启动命令执行成功，等待应用加载
2025-07-27 10:41:09 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram应用启动命令执行完成
2025-07-27 10:41:09 - InstagramDMTask - INFO - [模拟器1] Instagram启动检测 第1/3次
2025-07-27 10:41:17 - InstagramDMTask - INFO - [模拟器1] ❌ 批量验证失败
2025-07-27 10:41:17 - InstagramDMTask - INFO - [模拟器1] ❌ 验证失败
2025-07-27 10:41:19 - InstagramDMTask - INFO - [模拟器1] Instagram启动检测中... 已等待10.2秒
2025-07-27 10:41:22 - InstagramDMTask - INFO - [模拟器1] ✅ 批量验证成功
2025-07-27 10:41:22 - InstagramDMTask - INFO - [模拟器1] ✅ 验证成功
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 开始执行关注任务，模式: fans
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 开始执行【模式二：关注粉丝模式】
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 👥 开始处理目标用户: rissan_story (关注其粉丝)
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 开始处理用户：rissan_story
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] 跳转到用户主页：rissan_story
2025-07-27 10:41:22 - InstagramFollowTask - INFO - [模拟器1] ADB命令执行成功: Starting: Intent { act=android.intent.action.VIEW dat=instagram://user?username=rissan_story }
2025-07-27 10:41:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:41:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:41:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:41:26 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:41:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:41:27 - InstagramFollowTask - INFO - [模拟器1] ✅ 标题栏验证成功，当前在用户 rissan_story 的资料页
2025-07-27 10:41:27 - InstagramFollowTask - INFO - [模拟器1] 当前已在用户主页：rissan_story
2025-07-27 10:41:33 - InstagramFollowTask - INFO - [模拟器1] 🔍 蓝V检测: 未找到认证标识元素
2025-07-27 10:41:41 - InstagramFollowTask - INFO - [模拟器1] 成功打开粉丝列表
2025-07-27 10:41:41 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 找到 7 个粉丝容器
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.26秒，找到6个粉丝
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: ʜᴏɴᴏ 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: はるな / 𝗁𝖺𝗋𝗎𝗇𝖺 🎀 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: 𝙈𝙄𝙐 ㅤ💘 24歳の"リアル"な挑戦物語🌏✈️ 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: こうへい 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: tomoko 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 用户: 長谷川琉奈 不符合地区筛选,跳过
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.63秒
2025-07-27 10:41:42 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 10:41:43 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 10:41:43 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 成功提取 7 个粉丝信息
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：7个粉丝，滚动7个位置，距离350px
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.45秒，找到7个粉丝
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: 長谷川琉奈 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: Mad☺︎ka 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: 🅰️y🅰️k🅰️🎧🩶 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: akarin 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: 【𝗔𝗶 𝘁𝗼 𝗫.】𝙂𝙞𝙧𝙡𝙨 𝙀𝙢𝙥𝙤𝙬𝙚𝙧𝙢𝙚𝙣𝙩 𝘾𝙤𝙢𝙢𝙪𝙣𝙞𝙩𝙮 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: タカハシ 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 用户: kiyo 不符合地区筛选,跳过
2025-07-27 10:41:45 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 10:41:46 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动7个位置，距离350px，耗时0.65秒
2025-07-27 10:41:46 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 10:41:46 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:41:46 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:41:46 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:41:46 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:41:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:41:47 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 10:41:47 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 成功提取 7 个粉丝信息
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：7个粉丝，滚动7个位置，距离350px
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.25秒，找到7个粉丝
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: kiyo 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: 桜 颯 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: ミサト🐯💋 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: no-mu 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: masumura komugi 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: shibaraku 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 用户: しろう 不符合地区筛选,跳过
2025-07-27 10:41:48 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 10:41:49 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动7个位置，距离350px，耗时0.68秒
2025-07-27 10:41:49 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 10:41:50 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 10:41:50 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.25秒，找到6个粉丝
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: しろう 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: Doula Doula 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: 𝘳𝘶𝘳𝘪 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: パみ 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: ｍａｒｉｎ 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 用户: tanaka yuka 不符合地区筛选,跳过
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.63秒
2025-07-27 10:41:51 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 10:41:52 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 10:41:52 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.27秒，找到6个粉丝
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: 𝘳𝘶𝘳𝘪 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: パみ 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: ｍａｒｉｎ 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: tanaka yuka 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: ライバー事務所YOUPACEスカウトチーム 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 用户: wear tutta 不符合地区筛选,跳过
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.66秒
2025-07-27 10:41:54 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 10:41:55 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 10:41:55 - InstagramFollowTask - INFO - [模拟器1] 总任务超时(66秒)，退出
2025-07-27 10:41:55 - InstagramFollowTask - INFO - [模拟器1] 📊 目标用户 rissan_story 处理结果: 总任务超时(66秒)，退出
2025-07-27 10:41:55 - InstagramFollowTask - INFO - [模拟器1] ⏱️ 切换用户延迟: 433毫秒
2025-07-27 10:41:56 - InstagramFollowTask - INFO - [模拟器1] 总任务超时(66秒).任务进度: 0 / 2 耗时: 67.24秒
2025-07-27 10:41:56 - InstagramFollowTask - INFO - [模拟器1] ✅ 【模式二：关注粉丝循环】完成，成功关注: 0, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置观察者已注销
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] 开始任务完成后清理工作
2025-07-27 10:41:56 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已从任务活动监控移除
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] 心跳监控已移除
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] 准备关闭模拟器
2025-07-27 10:41:56 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 1 | remaining_running: 0
2025-07-27 10:41:56 - Emulator - INFO - 模拟器停止成功 | emulator_id: 1
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] 模拟器已成功关闭
2025-07-27 10:41:56 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务stop
2025-07-27 10:41:56 - InstagramDMTask - INFO - [模拟器1] 任务完成后清理工作完成
2025-07-27 10:41:56 - MainWindowV2 - INFO - 模拟器1停止成功
2025-07-27 10:41:56 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 10:41:56 - InstagramFollowTaskThread - INFO - [模拟器1] Instagram关注粉丝任务执行成功
2025-07-27 10:41:56 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 10:41:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 10:41:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 排队中 | new_state: 启动中
2025-07-27 10:41:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 10:41:56 - MainWindowV2 - INFO - 模拟器2: 排队中 -> 启动中
2025-07-27 10:41:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 2
2025-07-27 10:41:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:41:56 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 10:41:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 2
2025-07-27 10:42:09 - Emulator - INFO - Android系统启动完成 | emulator_id: 2 | elapsed_time: 13.0秒
2025-07-27 10:42:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 启动中 -> 运行中
2025-07-27 10:42:09 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 启动中 | new_state: 运行中
2025-07-27 10:42:09 - InstagramFollowTaskManager - INFO - 启动模拟器2的Instagram关注任务线程 - 当前并发: 2/1
2025-07-27 10:42:09 - TaskActivityHeartbeatManager - INFO - 模拟器 2 已添加到任务活动监控，失败计数: 0
2025-07-27 10:42:09 - Emulator - INFO - 模拟器启动成功 | emulator_id: 2 | running_count: 1
2025-07-27 10:42:09 - MainWindowV2 - INFO - 任务完成: 模拟器2, 任务start
2025-07-27 10:42:09 - MainWindowV2 - INFO - 模拟器2启动成功
2025-07-27 10:42:09 - InstagramTaskThread - INFO - [模拟器2] 开始智能等待启动完成
2025-07-27 10:42:09 - InstagramTaskThread - INFO - [模拟器2] 检测到模拟器已通过任务接力启动，总等待时间: 0.0秒
2025-07-27 10:42:09 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=2, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 10:42:09 - InstagramTaskThread - INFO - [模拟器2] 开始窗口排列
2025-07-27 10:42:09 - WindowArrangementManager - INFO - 模拟器2启动完成，立即触发窗口排列
2025-07-27 10:42:09 - MainWindowV2 - WARNING - 未找到模拟器2，无法更新状态
2025-07-27 10:42:09 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
2025-07-27 10:42:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:42:09 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 10:42:10 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中, PID: 20112
2025-07-27 10:42:10 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
2025-07-27 10:42:10 - StartupManager - INFO - 调度器已停止
2025-07-27 10:42:11 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 10:42:11 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 10:42:12 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 10:42:12 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 10:42:17 - InstagramTaskThread - INFO - [模拟器2] 窗口排列完成
2025-07-27 10:42:17 - InstagramFollowTaskThread - INFO - [模拟器2] 开始执行Instagram关注粉丝任务
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-27 10:42:17 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-27 10:42:17 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务配置加载完成
2025-07-27 10:42:17 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务执行器初始化完成
2025-07-27 10:42:17 - InstagramFollowTask - INFO - [模拟器2] 关注模式已设置为: fans
2025-07-27 10:42:17 - InstagramFollowTask - INFO - [模拟器2] 开始执行Instagram关注任务
2025-07-27 10:42:17 - InstagramFollowTask - WARNING - [模拟器2] 任务开始时间未由线程传递，在此设置
2025-07-27 10:42:17 - InstagramFollowTask - INFO - [模拟器2] 任务超时设置: 66秒，已运行: 0.00秒
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] 模拟器Android系统运行正常，桌面稳定
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] 开始检查应用安装状态
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] 📊 应用安装状态检测结果:
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] ✅ 所有必要应用已安装
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] 开始启动V2Ray应用
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] V2Ray启动命令执行成功，等待应用加载
2025-07-27 10:42:18 - InstagramDMTask - INFO - [模拟器2] V2Ray应用启动结果: 成功
2025-07-27 10:42:21 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray应用启动成功
2025-07-27 10:42:21 - InstagramDMTask - INFO - [模拟器2] 开始检查V2Ray节点列表状态
2025-07-27 10:42:22 - InstagramDMTask - INFO - [模拟器2] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 10:42:22 - InstagramDMTask - INFO - [模拟器2] 开始连接V2Ray节点
2025-07-27 10:42:24 - InstagramDMTask - INFO - [模拟器2] 当前连接状态: 未连接
2025-07-27 10:42:24 - InstagramDMTask - INFO - [模拟器2] V2Ray节点未连接，开始连接
2025-07-27 10:42:25 - InstagramDMTask - INFO - [模拟器2] 已点击连接按钮，等待连接完成
2025-07-27 10:42:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:42:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:42:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:42:26 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:42:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:42:27 - InstagramDMTask - INFO - [模拟器2] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 10:42:27 - InstagramDMTask - INFO - [模拟器2] V2Ray节点连接成功
2025-07-27 10:42:27 - InstagramDMTask - INFO - [模拟器2] 开始测试V2Ray节点延迟
2025-07-27 10:42:27 - InstagramDMTask - INFO - [模拟器2] 开始V2Ray节点延迟测试
2025-07-27 10:42:28 - InstagramDMTask - INFO - [模拟器2] 当前测试状态: 已连接，点击测试连接
2025-07-27 10:42:28 - InstagramDMTask - INFO - [模拟器2] 点击开始延迟测试
2025-07-27 10:42:29 - InstagramDMTask - INFO - [模拟器2] 已点击测试按钮，等待测试结果
2025-07-27 10:42:30 - InstagramDMTask - INFO - [模拟器2] 测试状态监控 (1/30): 连接成功：延时 266 毫秒
2025-07-27 10:42:30 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray节点延迟测试成功: 连接成功：延时 266 毫秒
2025-07-27 10:42:30 - InstagramDMTask - INFO - [模拟器2] 等待5秒后进入下一阶段
2025-07-27 10:42:35 - InstagramDMTask - INFO - [模拟器2] 开始启动Instagram应用
2025-07-27 10:42:35 - InstagramDMTask - INFO - [模拟器2] Instagram启动命令执行成功，等待应用加载
2025-07-27 10:42:38 - InstagramDMTask - INFO - [模拟器2] ✅ Instagram应用启动命令执行完成
2025-07-27 10:42:38 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测 第1/3次
2025-07-27 10:42:44 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:42:44 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:42:46 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:42:46 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:42:46 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:42:46 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:42:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:42:49 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测中... 已等待10.5秒
2025-07-27 10:42:51 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:42:51 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:42:53 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测中... 已等待15.3秒
2025-07-27 10:42:56 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:42:56 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:42:58 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测中... 已等待20.2秒
2025-07-27 10:43:01 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:43:01 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:43:03 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测中... 已等待24.8秒
2025-07-27 10:43:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:43:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:43:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:43:06 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:43:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:43:09 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测中... 已等待30.4秒
2025-07-27 10:43:11 - InstagramDMTask - INFO - [模拟器2] ✅ 批量验证成功
2025-07-27 10:43:11 - InstagramDMTask - INFO - [模拟器2] ✅ 验证成功
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 开始执行关注任务，模式: fans
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 开始执行【模式二：关注粉丝模式】
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 👥 开始处理目标用户: ___kie.k___ (关注其粉丝)
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：___kie.k___
2025-07-27 10:43:11 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：___kie.k___
2025-07-27 10:43:12 - InstagramFollowTask - INFO - [模拟器2] ADB命令执行成功: Starting: Intent { act=android.intent.action.VIEW dat=instagram://user?username=___kie.k___ }
2025-07-27 10:43:17 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 ___kie.k___ 的资料页
2025-07-27 10:43:17 - InstagramFollowTask - INFO - [模拟器2] 当前已在用户主页：___kie.k___
2025-07-27 10:43:22 - InstagramFollowTask - INFO - [模拟器2] 🔍 蓝V检测: 未找到认证标识元素
2025-07-27 10:43:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:43:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:43:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:43:26 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:43:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] 成功打开粉丝列表
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] 总任务超时(66秒)，退出
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] 📊 目标用户 ___kie.k___ 处理结果: 总任务超时(66秒)，退出
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 448毫秒
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] 总任务超时(66秒).任务进度: 0 / 2 耗时: 72.82秒
2025-07-27 10:43:30 - InstagramFollowTask - INFO - [模拟器2] ✅ 【模式二：关注粉丝循环】完成，成功关注: 0, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置观察者已注销
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] 开始任务完成后清理工作
2025-07-27 10:43:30 - TaskActivityHeartbeatManager - INFO - 模拟器 2 已从任务活动监控移除
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] 心跳监控已移除
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] 准备关闭模拟器
2025-07-27 10:43:30 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 2 | remaining_running: 0
2025-07-27 10:43:30 - Emulator - INFO - 模拟器停止成功 | emulator_id: 2
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] 模拟器已成功关闭
2025-07-27 10:43:30 - InstagramDMTask - INFO - [模拟器2] 任务完成后清理工作完成
2025-07-27 10:43:30 - InstagramFollowTaskThread - INFO - [模拟器2] Instagram关注粉丝任务执行成功
2025-07-27 10:43:30 - MainWindowV2 - INFO - 任务完成: 模拟器2, 任务stop
2025-07-27 10:43:30 - MainWindowV2 - INFO - 模拟器2停止成功
2025-07-27 10:43:30 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=2, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 10:43:30 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 10:43:36 - MainWindowV2 - INFO - 开始重新扫描...
2025-07-27 10:43:36 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 手动刷新)
2025-07-27 10:43:36 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 10:43:36 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 10:43:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 10:43:36 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 10:43:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 10:43:36 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 10:43:36 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 10:43:36 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 10:43:36 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 10:43:36 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 10:43:36 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.23s | count: 1229
2025-07-27 10:43:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 10:43:36 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 10:43:36 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 10:43:36 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 10:43:38 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 10:43:38 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 10:43:38 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 10:43:38 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-27 10:43:41 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及2个模拟器
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-27 10:43:41 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 2
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: 2
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [1, 2]
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-27 10:43:41 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-27 10:43:41 - StartupManager - INFO - 启动调度器已启动
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 10:43:41 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-27 10:43:41 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-27 10:43:41 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 10:43:41 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-27 10:43:41 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-27 10:43:41 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 10:43:41 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-27 10:43:41 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-27 10:43:41 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-27 10:43:41 - InstagramTaskManager - INFO - 启动模拟器1的Instagram任务线程 - 当前并发: 1/1
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-27 10:43:41 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 10:43:41 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 10:43:41 - InstagramTaskThread - INFO - [模拟器1] 开始智能等待启动完成
2025-07-27 10:43:41 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 10:43:41 - InstagramTaskThread - INFO - [模拟器1] 排队阶段完成，等待时间: 0.0秒，开始启动阶段
2025-07-27 10:43:41 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 1
2025-07-27 10:43:41 - InstagramTaskThread - INFO - [模拟器1] 开始启动阶段，超时时间: 25秒
2025-07-27 10:43:41 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-27 10:43:41 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及2个模拟器（线程池并发模式）
2025-07-27 10:43:41 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 排队中 | new_state: 启动中
2025-07-27 10:43:41 - MainWindowV2 - INFO - 模拟器1: 排队中 -> 启动中
2025-07-27 10:43:41 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:43:41 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 10:43:41 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 1
2025-07-27 10:43:51 - Emulator - INFO - Android系统启动完成 | emulator_id: 1 | elapsed_time: 9.8秒
2025-07-27 10:43:51 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 启动中 -> 运行中
2025-07-27 10:43:51 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 启动中 | new_state: 运行中
2025-07-27 10:43:51 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器1状态变化: 启动中 -> 运行中
2025-07-27 10:43:51 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已添加到任务活动监控，失败计数: 0
2025-07-27 10:43:51 - Emulator - INFO - 模拟器启动成功 | emulator_id: 1 | running_count: 1
2025-07-27 10:43:51 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务start
2025-07-27 10:43:51 - MainWindowV2 - INFO - 模拟器1启动成功
2025-07-27 10:43:51 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 10:43:51 - WindowArrangementManager - INFO - 模拟器1启动完成，立即触发窗口排列
2025-07-27 10:43:51 - MainWindowV2 - WARNING - 未找到模拟器1，无法更新状态
2025-07-27 10:43:51 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 10:43:51 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:43:51 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 10:43:51 - InstagramTaskThread - INFO - [模拟器1] 启动完成，启动阶段: 10.0秒，总时间: 10.0秒
2025-07-27 10:43:51 - InstagramTaskThread - INFO - [模拟器1] 开始窗口排列
2025-07-27 10:43:51 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中, PID: 12228
2025-07-27 10:43:51 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 10:43:53 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 10:43:53 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 10:43:53 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 10:43:59 - InstagramTaskThread - INFO - [模拟器1] 窗口排列完成
2025-07-27 10:43:59 - InstagramTaskThread - INFO - [模拟器1] 开始执行Instagram私信任务
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 雷电模拟器API初始化成功
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 已设置ld.emulator_id = 1
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置热加载观察者已注册
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] Instagram私信任务执行器初始化完成
2025-07-27 10:43:59 - InstagramTaskThread - INFO - [模拟器1] 任务超时计时已从线程启动开始: 66秒
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 开始执行Instagram私信任务
2025-07-27 10:43:59 - InstagramDMTask - INFO - [模拟器1] 任务超时设置: 66秒，已运行: 18.01秒
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] 模拟器Android系统运行正常，桌面稳定
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] 开始检查应用安装状态
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] 📊 应用安装状态检测结果:
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] ✅ 所有必要应用已安装
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] 开始启动V2Ray应用
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] V2Ray启动命令执行成功，等待应用加载
2025-07-27 10:44:00 - InstagramDMTask - INFO - [模拟器1] V2Ray应用启动结果: 成功
2025-07-27 10:44:03 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray应用启动成功
2025-07-27 10:44:03 - InstagramDMTask - INFO - [模拟器1] 开始检查V2Ray节点列表状态
2025-07-27 10:44:04 - InstagramDMTask - INFO - [模拟器1] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 10:44:04 - InstagramDMTask - INFO - [模拟器1] 开始连接V2Ray节点
2025-07-27 10:44:05 - InstagramDMTask - INFO - [模拟器1] 当前连接状态: 未连接
2025-07-27 10:44:05 - InstagramDMTask - INFO - [模拟器1] V2Ray节点未连接，开始连接
2025-07-27 10:44:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:44:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:44:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:44:06 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:44:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:44:07 - InstagramDMTask - INFO - [模拟器1] 已点击连接按钮，等待连接完成
2025-07-27 10:44:09 - InstagramDMTask - INFO - [模拟器1] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 10:44:09 - InstagramDMTask - INFO - [模拟器1] V2Ray节点连接成功
2025-07-27 10:44:09 - InstagramDMTask - INFO - [模拟器1] 开始测试V2Ray节点延迟
2025-07-27 10:44:09 - InstagramDMTask - INFO - [模拟器1] 开始V2Ray节点延迟测试
2025-07-27 10:44:10 - InstagramDMTask - INFO - [模拟器1] 当前测试状态: 已连接，点击测试连接
2025-07-27 10:44:10 - InstagramDMTask - INFO - [模拟器1] 点击开始延迟测试
2025-07-27 10:44:10 - InstagramDMTask - INFO - [模拟器1] 已点击测试按钮，等待测试结果
2025-07-27 10:44:12 - InstagramDMTask - INFO - [模拟器1] 测试状态监控 (1/30): 连接成功：延时 276 毫秒
2025-07-27 10:44:12 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray节点延迟测试成功: 连接成功：延时 276 毫秒
2025-07-27 10:44:12 - InstagramDMTask - INFO - [模拟器1] 等待5秒后进入下一阶段
2025-07-27 10:44:17 - InstagramDMTask - INFO - [模拟器1] 开始启动Instagram应用
2025-07-27 10:44:17 - InstagramDMTask - INFO - [模拟器1] Instagram启动命令执行成功，等待应用加载
2025-07-27 10:44:20 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram应用启动命令执行完成
2025-07-27 10:44:20 - InstagramDMTask - INFO - [模拟器1] Instagram启动检测 第1/3次
2025-07-27 10:44:25 - InstagramDMTask - INFO - [模拟器1] ✅ 批量验证成功
2025-07-27 10:44:25 - InstagramDMTask - INFO - [模拟器1] ✅ 验证成功
2025-07-27 10:44:25 - InstagramDMTask - INFO - [模拟器1] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 10:44:25 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 10:44:25 - InstagramDMTask - INFO - [模拟器1] 开始导航到个人主页
2025-07-27 10:44:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:44:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:44:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 10:44:26 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 10:44:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:44:29 - InstagramDMTask - INFO - [模拟器1] ✅ 个人主页加载完成
2025-07-27 10:44:29 - InstagramDMTask - INFO - [模拟器1] 开始获取粉丝数量信息
2025-07-27 10:44:30 - InstagramDMTask - INFO - [模拟器1] 原始粉丝数文本: 8
2025-07-27 10:44:30 - InstagramDMTask - INFO - [模拟器1] ✅ 粉丝数量验证通过: 8
2025-07-27 10:44:30 - InstagramDMTask - INFO - [模拟器1] 开始打开粉丝列表
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] ✅ 粉丝列表加载完成
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 开始初始化私信任务
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 已加载 9 条去重记录
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 任务目标数量: 25
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 延迟范围: 2-200ms
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] ✅ 私信任务初始化完成
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 开始批量私信发送循环
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 初始化循环参数
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 开始主循环，目标数量: 25
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 当前进度: 0/25
2025-07-27 10:44:35 - InstagramDMTask - INFO - [模拟器1] 开始批量获取当前屏幕可见的粉丝列表
2025-07-27 10:44:35 - MainWindowV2 - INFO - 模拟器1的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 找到 6 个粉丝
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: wavtvavdwavrva
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: wavyvawvhvawv
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 找到 5 个可见粉丝
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 用户 wavtvavdwavrva 已发送过，跳过
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 用户 wavyvawvhvawv 已发送过，跳过
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 用户 wavtvawavrav 已发送过，跳过
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 用户 awdvtvdwahad 已发送过，跳过
2025-07-27 10:44:36 - InstagramDMTask - INFO - [模拟器1] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-27 10:44:39 - InstagramDMTask - INFO - [模拟器1] 滚动粉丝列表
2025-07-27 10:44:40 - InstagramDMTask - INFO - [模拟器1] 滑动6个容器中的5个位置，距离250px
2025-07-27 10:44:42 - InstagramDMTask - INFO - [模拟器1] 当前进度: 0/25
2025-07-27 10:44:42 - InstagramDMTask - INFO - [模拟器1] 开始批量获取当前屏幕可见的粉丝列表
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] 找到 3 个粉丝
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] ✅ 找到符合条件的粉丝: wavtvawavtva242
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] 找到 2 个可见粉丝
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] 用户 dawvtwavtvadawvt 已发送过，跳过
2025-07-27 10:44:43 - InstagramDMTask - INFO - [模拟器1] 用户 wavtvawavtva242 已发送过，跳过
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] 已到达粉丝列表底部
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] 已到达粉丝列表底部
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] ✅ 批量私信发送完成，共发送 0 条私信
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置观察者已注销
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] 开始任务完成后清理工作
2025-07-27 10:44:45 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已从任务活动监控移除
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] 心跳监控已移除
2025-07-27 10:44:45 - InstagramDMTask - INFO - [模拟器1] 准备关闭模拟器
2025-07-27 10:44:45 - MainWindowV2 - WARNING - 模拟器1的Instagram任务状态更新失败
2025-07-27 10:44:46 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 1 | remaining_running: 0
2025-07-27 10:44:46 - Emulator - INFO - 模拟器停止成功 | emulator_id: 1
2025-07-27 10:44:46 - InstagramDMTask - INFO - [模拟器1] 模拟器已成功关闭
2025-07-27 10:44:46 - InstagramDMTask - INFO - [模拟器1] 任务完成后清理工作完成
2025-07-27 10:44:46 - InstagramTaskThread - INFO - [模拟器1] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-27 10:44:46 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务stop
2025-07-27 10:44:46 - MainWindowV2 - INFO - 模拟器1停止成功
2025-07-27 10:44:46 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 10:44:46 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 10:44:46 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 10:44:46 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 排队中 | new_state: 启动中
2025-07-27 10:44:46 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 10:44:46 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 10:44:46 - MainWindowV2 - INFO - 模拟器2: 排队中 -> 启动中
2025-07-27 10:44:46 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 2
2025-07-27 10:44:46 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:44:46 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 10:44:46 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 2
2025-07-27 10:44:56 - Emulator - INFO - Android系统启动完成 | emulator_id: 2 | elapsed_time: 9.8秒
2025-07-27 10:44:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 启动中 -> 运行中
2025-07-27 10:44:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 启动中 | new_state: 运行中
2025-07-27 10:44:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 启动中 -> 运行中
2025-07-27 10:44:56 - TaskActivityHeartbeatManager - INFO - 模拟器 2 已添加到任务活动监控，失败计数: 0
2025-07-27 10:44:56 - InstagramTaskManager - INFO - 启动模拟器2的Instagram私信任务线程 - 当前并发: 2/1
2025-07-27 10:44:56 - Emulator - INFO - 模拟器启动成功 | emulator_id: 2 | running_count: 1
2025-07-27 10:44:56 - MainWindowV2 - INFO - 任务完成: 模拟器2, 任务start
2025-07-27 10:44:56 - MainWindowV2 - INFO - 模拟器2启动成功
2025-07-27 10:44:56 - InstagramTaskThread - INFO - [模拟器2] 开始智能等待启动完成
2025-07-27 10:44:56 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=2, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 10:44:56 - InstagramTaskThread - INFO - [模拟器2] 检测到模拟器已通过任务接力启动，总等待时间: 0.0秒
2025-07-27 10:44:56 - InstagramTaskThread - INFO - [模拟器2] 开始窗口排列
2025-07-27 10:44:56 - WindowArrangementManager - INFO - 模拟器2启动完成，立即触发窗口排列
2025-07-27 10:44:56 - MainWindowV2 - WARNING - 未找到模拟器2，无法更新状态
2025-07-27 10:44:56 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
2025-07-27 10:44:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 10:44:56 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 10:44:56 - StartupManager - INFO - 调度器已停止
2025-07-27 10:44:57 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中, PID: 16452
2025-07-27 10:44:57 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
2025-07-27 10:44:58 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 10:44:58 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 10:44:58 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 10:45:04 - InstagramTaskThread - INFO - [模拟器2] 窗口排列完成
2025-07-27 10:45:04 - InstagramTaskThread - INFO - [模拟器2] 开始执行Instagram私信任务
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-27 10:45:04 - InstagramTaskThread - INFO - [模拟器2] 任务超时计时已从线程启动开始: 66秒
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 开始执行Instagram私信任务
2025-07-27 10:45:04 - InstagramDMTask - INFO - [模拟器2] 任务超时设置: 66秒，已运行: 8.01秒
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] 模拟器Android系统运行正常，桌面稳定
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] 开始检查应用安装状态
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] 📊 应用安装状态检测结果:
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] ✅ 所有必要应用已安装
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] 开始启动V2Ray应用
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] V2Ray启动命令执行成功，等待应用加载
2025-07-27 10:45:05 - InstagramDMTask - INFO - [模拟器2] V2Ray应用启动结果: 成功
2025-07-27 10:45:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:45:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:45:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:45:06 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:45:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:45:08 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray应用启动成功
2025-07-27 10:45:08 - InstagramDMTask - INFO - [模拟器2] 开始检查V2Ray节点列表状态
2025-07-27 10:45:09 - InstagramDMTask - INFO - [模拟器2] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 10:45:09 - InstagramDMTask - INFO - [模拟器2] 开始连接V2Ray节点
2025-07-27 10:45:10 - InstagramDMTask - INFO - [模拟器2] 当前连接状态: 未连接
2025-07-27 10:45:10 - InstagramDMTask - INFO - [模拟器2] V2Ray节点未连接，开始连接
2025-07-27 10:45:12 - InstagramDMTask - INFO - [模拟器2] 已点击连接按钮，等待连接完成
2025-07-27 10:45:14 - InstagramDMTask - INFO - [模拟器2] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 10:45:14 - InstagramDMTask - INFO - [模拟器2] V2Ray节点连接成功
2025-07-27 10:45:14 - InstagramDMTask - INFO - [模拟器2] 开始测试V2Ray节点延迟
2025-07-27 10:45:14 - InstagramDMTask - INFO - [模拟器2] 开始V2Ray节点延迟测试
2025-07-27 10:45:15 - InstagramDMTask - INFO - [模拟器2] 当前测试状态: 已连接，点击测试连接
2025-07-27 10:45:15 - InstagramDMTask - INFO - [模拟器2] 点击开始延迟测试
2025-07-27 10:45:15 - InstagramDMTask - INFO - [模拟器2] 已点击测试按钮，等待测试结果
2025-07-27 10:45:17 - InstagramDMTask - INFO - [模拟器2] 测试状态监控 (1/30): 连接成功：延时 258 毫秒
2025-07-27 10:45:17 - InstagramDMTask - INFO - [模拟器2] ✅ V2Ray节点延迟测试成功: 连接成功：延时 258 毫秒
2025-07-27 10:45:17 - InstagramDMTask - INFO - [模拟器2] 等待5秒后进入下一阶段
2025-07-27 10:45:22 - InstagramDMTask - INFO - [模拟器2] 开始启动Instagram应用
2025-07-27 10:45:22 - InstagramDMTask - INFO - [模拟器2] Instagram启动命令执行成功，等待应用加载
2025-07-27 10:45:25 - InstagramDMTask - INFO - [模拟器2] ✅ Instagram应用启动命令执行完成
2025-07-27 10:45:25 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测 第1/3次
2025-07-27 10:45:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:45:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:45:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:45:26 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:45:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:45:27 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:45:27 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:45:27 - InstagramDMTask - INFO - [模拟器2] 第1次启动超时，重启Instagram...
2025-07-27 10:45:27 - InstagramDMTask - INFO - [模拟器2] 重启Instagram...
2025-07-27 10:45:43 - InstagramDMTask - INFO - [模拟器2] Instagram重启完成
2025-07-27 10:45:43 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测 第2/3次
2025-07-27 10:45:45 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:45:45 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:45:45 - InstagramDMTask - INFO - [模拟器2] 检测到Instagram应用崩溃，尝试点击应用按钮继续
2025-07-27 10:45:46 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:45:46 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:45:46 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:45:46 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:45:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:45:47 - InstagramDMTask - INFO - [模拟器2] 成功点击关闭应用按钮，等待3秒后继续
2025-07-27 10:45:50 - InstagramDMTask - INFO - [模拟器2] Instagram启动检测 第3/3次
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] ❌ 批量验证失败
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] ❌ 验证失败
2025-07-27 10:45:52 - InstagramDMTask - ERROR - [模拟器2] Instagram三次启动均失败
2025-07-27 10:45:52 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-27 10:45:52 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-27 10:45:52 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-27 10:45:52 - NativeScreenshotEngine - INFO - 开始截取模拟器 2 的截图
2025-07-27 10:45:52 - NativeScreenshotEngine - INFO - 模拟器 2 截图成功: test_screenshots\emulator_2_Instagram_Instagram启动失败_1_20250727_104552.png
2025-07-27 10:45:52 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_2_Instagram_Instagram启动失败_1_20250727_104552.png
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] 异常状态截图已保存: Instagram启动失败
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] 任务终止原因: Instagram启动失败
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] 开始关闭模拟器...
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] 模拟器已关闭
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] 任务状态更新: 失败 - Instagram启动失败
2025-07-27 10:45:52 - InstagramDMTask - INFO - [模拟器2] Instagram页面状态检测结果: 任务终止-Instagram启动失败
2025-07-27 10:45:52 - InstagramDMTask - ERROR - [模拟器2] ❌ Instagram任务终止
2025-07-27 10:45:52 - InstagramTaskThread - ERROR - [模拟器2] Instagram任务执行失败: 任务终止-Instagram启动失败
2025-07-27 10:46:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:46:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:46:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 2
2025-07-27 10:46:06 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:46:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:46:26 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:46:26 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:46:26 - TaskActivityHeartbeatManager - INFO - 模拟器 2 疑似心跳异常，进入观察期 | 无活动时间: 33.8秒
2025-07-27 10:46:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 10:46:46 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - ERROR - 模拟器 2 确认心跳异常 | 总无活动时间: 53.8秒
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 2
2025-07-27 10:46:46 - NativeScreenshotEngine - INFO - 开始截取模拟器 2 的截图
2025-07-27 10:46:46 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 2
2025-07-27 10:46:46 - MainWindowV2 - INFO - 模拟器2心跳异常: 心跳异常 (失败次数: 1)
2025-07-27 10:46:46 - MainWindowV2 - WARNING - 模拟器2心跳检测失败: 心跳异常, 失败次数: 1
2025-07-27 10:46:46 - MainWindowV2 - WARNING - 模拟器2心跳状态更新未产生变化
2025-07-27 10:46:46 - NativeScreenshotEngine - ERROR - 模拟器 2 绑定句柄无效: 0
2025-07-27 10:46:46 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 2
2025-07-27 10:46:46 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 2 | remaining_running: 0
2025-07-27 10:46:46 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 运行中 -> 异常
2025-07-27 10:46:46 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 运行中 | new_state: 异常
2025-07-27 10:46:46 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 运行中 -> 异常
2025-07-27 10:46:46 - MainWindowV2 - INFO - 模拟器2: 运行中 -> 异常
2025-07-27 10:46:46 - InstagramTaskManager - INFO - 清理模拟器2的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-27 10:46:46 - InstagramTaskManager - INFO - 模拟器2的Instagram线程已清理完成，当前并发: 1/1
2025-07-27 10:46:46 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 运行中 -> 异常
2025-07-27 10:46:46 - InstagramTaskManager - INFO - 清理模拟器2的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-27 10:46:46 - InstagramTaskManager - INFO - 模拟器2的Instagram线程已清理完成，当前并发: 1/1
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 2 | removed_from_running: True | removed_from_active: False | total_running: 0 | total_active: 0
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 模拟器 2 已从任务活动监控移除
2025-07-27 10:46:46 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-27 10:46:46 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-27 10:46:46 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-27 10:46:46 - UnifiedEmulatorManager - INFO - 模拟器已切换: 2 -> -1
2025-07-27 10:46:46 - MainWindowV2 - INFO - 模拟器自动切换: 2 -> -1
2025-07-27 10:46:46 - MainWindowV2 - INFO - 模拟器已自动切换: 2 -> -1
2025-07-27 10:46:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
