2025-07-27 16:15:33 - root - INFO - 简化日志系统初始化完成
2025-07-27 16:15:33 - App - INFO - 开始Instagram直接关注真实流程测试
2025-07-27 16:15:33 - App - INFO - [直接关注测试器] ⚡ 开始Instagram直接关注快速测试
2025-07-27 16:15:33 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-27 16:15:33 - App - INFO - [直接关注测试器] 开始时间: 2025-07-27 16:15:33
2025-07-27 16:15:33 - App - INFO - [直接关注测试器] ⚡ 快速模式：跳过阶段一、二、三，直接测试阶段四
2025-07-27 16:15:33 - App - INFO - [直接关注测试器] 开始设置真实测试环境
2025-07-27 16:15:33 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 16:15:33 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 16:15:33 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 16:15:33 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 16:15:33 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 16:15:33 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 16:15:33 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 16:15:33 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 16:15:33 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 16:15:33 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
