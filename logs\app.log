2025-07-27 17:49:16 - root - INFO - 简化日志系统初始化完成
2025-07-27 17:49:16 - main - INFO - 应用程序启动
2025-07-27 17:49:16 - __main__ - INFO - Qt应用程序已创建
2025-07-27 17:49:16 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 17:49:16 - __main__ - INFO - 统一配置管理器已创建
2025-07-27 17:49:16 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 17:49:16 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 17:49:16 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 17:49:16 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 17:49:16 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 17:49:16 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 17:49:16 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 17:49:16 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 17:49:16 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 17:49:16 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 17:49:16 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-27 17:49:17 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 17:49:17 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-27 17:49:17 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-27 17:49:17 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-27 17:49:17 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-27 17:49:17 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-27 17:49:17 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-27 17:49:17 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-27 17:49:17 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-27 17:49:17 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-27 17:49:17 - __main__ - INFO - UI主窗口已创建
2025-07-27 17:49:17 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-27 17:49:17 - __main__ - INFO - 主窗口已显示
2025-07-27 17:49:17 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 17:49:17 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 17:49:17 - __main__ - INFO - UI层和业务层已连接
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 17:49:17 - __main__ - INFO - 启动Qt事件循环
2025-07-27 17:49:17 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 17:49:17 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:17 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 17:49:17 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 17:49:17 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:17 - App - INFO - ldconsole命令执行成功，输出长度: 48391
2025-07-27 17:49:17 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 17:49:17 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 17:49:17 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 17:49:17 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 17:49:17 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.24s | count: 1229
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 17:49:17 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 17:49:17 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 17:49:17 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 17:49:18 - App - INFO - ldconsole命令执行成功，输出长度: 48391
2025-07-27 17:49:18 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 17:49:18 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 17:49:18 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 17:49:18 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 17:49:18 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.39s | count: 1229
2025-07-27 17:49:18 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 17:49:18 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 17:49:18 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 17:49:18 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 17:49:18 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-27 17:49:18 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-27 17:49:18 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-27 17:49:18 - __main__ - INFO - 后台服务已启动
2025-07-27 17:49:18 - __main__ - INFO - 延迟启动服务完成
2025-07-27 17:49:23 - MainWindowV2 - INFO - 已取消 1229 个勾选
2025-07-27 17:49:23 - MainWindowV2 - INFO - 已从第 4 行开始勾选 15 个模拟器
2025-07-27 17:49:28 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:29 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:30 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 17:49:30 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 17:49:30 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 17:49:30 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 17:49:32 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:32 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 17:49:32 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 17:49:32 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 17:49:32 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 17:49:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:49:36 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 17:49:36 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 17:49:36 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 17:49:36 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-27 17:49:40 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及15个模拟器
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-27 17:49:40 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 15
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 开始处理Instagram粉丝私信任务（线程池模式），模拟器数量: 15
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 15
2025-07-27 17:49:40 - StartupManager - INFO - 批量启动请求 | count: 15
2025-07-27 17:49:40 - StartupManager - INFO - 启动调度器已启动
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-27 17:49:40 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 17:49:40 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 3
2025-07-27 17:49:40 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 15
2025-07-27 17:49:40 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建15个Instagram线程
2025-07-27 17:49:40 - InstagramTaskManager - INFO - 启动模拟器3的Instagram任务线程 - 当前并发: 1/3
2025-07-27 17:49:40 - InstagramTaskManager - INFO - 启动模拟器4的Instagram任务线程 - 当前并发: 2/3
2025-07-27 17:49:40 - InstagramTaskManager - INFO - 启动模拟器5的Instagram任务线程 - 当前并发: 3/3
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-27 17:49:40 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 17:49:40 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-27 17:49:40 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-27 17:49:40 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-27 17:49:40 - MainWindowV2 - INFO - 15个模拟器: 未知 -> 排队中
2025-07-27 17:49:40 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-27 17:49:40 - InstagramTaskThread - INFO - [模拟器3] 等待启动中... 状态: 启动中, 已等待: 0.0秒
2025-07-27 17:49:40 - InstagramTaskThread - ERROR - [模拟器5] ❌ StartupManager已完成处理但未成功，状态: 排队中
2025-07-27 17:49:40 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 15 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 3
2025-07-27 17:49:40 - InstagramTaskThread - ERROR - [模拟器4] ❌ StartupManager已完成处理但未成功，状态: 排队中
2025-07-27 17:49:40 - MainWindowV2 - INFO - 启动进度: 排队15个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/3)
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 17:49:40 - MainWindowV2 - INFO - 批量启动完成: 0/15 成功
2025-07-27 17:49:40 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 15
2025-07-27 17:49:40 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-27 17:49:40 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及15个模拟器（线程池并发模式）
2025-07-27 17:49:40 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-27 17:49:40 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-27 17:49:40 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 14 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-27 17:49:40 - MainWindowV2 - INFO - 启动进度: 排队14个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/3)
2025-07-27 17:49:40 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-27 17:49:42 - InstagramTaskThread - INFO - [模拟器3] 等待启动中... 状态: 启动中, 已等待: 2.0秒
2025-07-27 17:49:44 - InstagramTaskThread - INFO - [模拟器3] 等待启动中... 状态: 启动中, 已等待: 4.0秒
2025-07-27 17:49:46 - InstagramTaskThread - INFO - [模拟器3] 等待启动中... 状态: 启动中, 已等待: 6.0秒
2025-07-27 17:49:48 - InstagramTaskThread - INFO - [模拟器3] 等待启动中... 状态: 启动中, 已等待: 8.0秒
2025-07-27 17:49:50 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 9.8秒
2025-07-27 17:49:50 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-27 17:49:50 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-27 17:49:50 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-27 17:49:50 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-27 17:49:50 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-27 17:49:50 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-27 17:49:50 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 17:49:50 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-27 17:49:50 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-27 17:49:50 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 17:49:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 14 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 6.666666666666667 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-27 17:49:50 - MainWindowV2 - INFO - 启动进度: 排队14个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/3)
2025-07-27 17:49:50 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 10.0秒
2025-07-27 17:49:50 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-27 17:49:50 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 5876
2025-07-27 17:49:50 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 17:49:52 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 17:49:52 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 17:49:52 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 17:49:55 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 17:49:55 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-27 17:49:55 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-27 17:49:55 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-27 17:49:55 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 13 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 6.666666666666667 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-27 17:49:55 - MainWindowV2 - INFO - 启动进度: 排队13个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/3)
2025-07-27 17:49:55 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-27 17:49:56 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:49:56 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:49:56 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 17:49:56 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 17:49:56 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:49:58 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-27 17:49:58 - InstagramTaskThread - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-27 17:49:58 - InstagramTaskThread - INFO - [模拟器3] 任务超时计时已从线程启动开始: 900秒
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 任务超时设置: 900秒，已运行: 18.13秒
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-27 17:49:58 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-27 17:49:59 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-27 17:50:02 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-27 17:50:02 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-27 17:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 17:50:03 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-27 17:50:04 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-27 17:50:04 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-27 17:50:05 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 9.9秒
2025-07-27 17:50:05 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-27 17:50:05 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-27 17:50:05 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-27 17:50:05 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-27 17:50:05 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-27 17:50:05 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-27 17:50:05 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 17:50:05 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-27 17:50:05 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-27 17:50:05 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 17:50:05 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 13 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 13.333333333333334 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-27 17:50:05 - MainWindowV2 - INFO - 启动进度: 排队13个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/3)
2025-07-27 17:50:05 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 16728
2025-07-27 17:50:05 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 17:50:06 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-27 17:50:07 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-27 17:50:07 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-27 17:50:07 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-27 17:50:08 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 17:50:08 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-27 17:50:08 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-27 17:50:08 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-27 17:50:09 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-27 17:50:09 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-27 17:50:09 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-27 17:50:10 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 17:50:10 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-27 17:50:10 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-27 17:50:10 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-27 17:50:10 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 12 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 13.333333333333334 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 17:50:10 - MainWindowV2 - INFO - 启动进度: 排队12个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-27 17:50:10 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-27 17:50:10 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 270 毫秒
2025-07-27 17:50:10 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 270 毫秒
2025-07-27 17:50:10 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-27 17:50:12 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:50:14 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 17:50:14 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 17:50:14 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 17:50:14 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 17:50:15 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-27 17:50:16 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-27 17:50:16 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-27 17:50:16 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:50:16 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 17:50:16 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 17:50:16 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-27 17:50:16 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-27 17:50:16 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:50:19 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-27 17:50:19 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/3次
2025-07-27 17:50:20 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 9.9秒
2025-07-27 17:50:20 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 17:50:20 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-27 17:50:20 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 3
2025-07-27 17:50:20 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-27 17:50:20 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-27 17:50:20 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-27 17:50:20 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 17:50:20 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-27 17:50:20 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-27 17:50:20 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 17:50:20 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 15 | queued: 12 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 20.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 17:50:20 - MainWindowV2 - INFO - 启动进度: 排队12个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-27 17:50:20 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 15320
2025-07-27 17:50:20 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 17:50:21 - InstagramDMTask - INFO - [模拟器3] ❌ 批量验证失败
2025-07-27 17:50:21 - InstagramDMTask - INFO - [模拟器3] ❌ 验证失败
2025-07-27 17:50:21 - InstagramDMTask - INFO - [模拟器3] 第1次启动超时，重启Instagram...
2025-07-27 17:50:21 - InstagramDMTask - INFO - [模拟器3] 重启Instagram...
2025-07-27 17:50:22 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 1)，保持不动
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 2): (604, 0) 保持原大小: 302x435
2025-07-27 17:50:22 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/3 个窗口
2025-07-27 17:50:22 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/3 个窗口 (成功: 1/3)
2025-07-27 17:50:36 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-27 17:50:36 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:50:36 - TaskActivityHeartbeatManager - INFO - 模拟器 4 疑似心跳异常，进入观察期 | 无活动时间: 30.9秒
2025-07-27 17:50:36 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 17:50:36 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 17:50:36 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:50:36 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:50:36 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:50:37 - InstagramDMTask - INFO - [模拟器3] Instagram重启完成
2025-07-27 17:50:37 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第2/3次
2025-07-27 17:50:39 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-27 17:50:39 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-27 17:50:39 - InstagramDMTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 17:50:39 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 17:50:39 - InstagramDMTask - INFO - [模拟器3] 开始导航到个人主页
2025-07-27 17:50:45 - InstagramDMTask - INFO - [模拟器3] ✅ 个人主页加载完成
2025-07-27 17:50:45 - InstagramDMTask - INFO - [模拟器3] 开始获取粉丝数量信息
2025-07-27 17:50:47 - InstagramDMTask - INFO - [模拟器3] 原始粉丝数文本: 6
2025-07-27 17:50:47 - InstagramDMTask - INFO - [模拟器3] ✅ 粉丝数量验证通过: 6
2025-07-27 17:50:47 - InstagramDMTask - INFO - [模拟器3] 开始打开粉丝列表
2025-07-27 17:50:50 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:50:50 - __main__ - INFO - 开始清理资源...
2025-07-27 17:50:50 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 17:50:50 - __main__ - INFO - 配置已保存
2025-07-27 17:50:50 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-27 17:50:50 - __main__ - INFO - 应用程序已退出
