2025-07-27 19:19:02 - root - INFO - 简化日志系统初始化完成
2025-07-27 19:19:02 - main - INFO - 应用程序启动
2025-07-27 19:19:02 - __main__ - INFO - Qt应用程序已创建
2025-07-27 19:19:02 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 19:19:02 - __main__ - INFO - 统一配置管理器已创建
2025-07-27 19:19:02 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 19:19:02 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 19:19:02 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 19:19:02 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 19:19:02 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 19:19:02 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 19:19:02 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 19:19:02 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 19:19:02 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 19:19:02 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 19:19:02 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-27 19:19:02 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 19:19:02 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-27 19:19:02 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-27 19:19:02 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-27 19:19:02 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-27 19:19:02 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-27 19:19:02 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-27 19:19:02 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-27 19:19:02 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-27 19:19:02 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-27 19:19:02 - __main__ - INFO - UI主窗口已创建
2025-07-27 19:19:02 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-27 19:19:03 - __main__ - INFO - 主窗口已显示
2025-07-27 19:19:03 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 19:19:03 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 19:19:03 - __main__ - INFO - UI层和业务层已连接
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 19:19:03 - __main__ - INFO - 启动Qt事件循环
2025-07-27 19:19:03 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 19:19:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:19:03 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 19:19:03 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 19:19:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:19:03 - App - INFO - ldconsole命令执行成功，输出长度: 48444
2025-07-27 19:19:03 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 19:19:03 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 19:19:03 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 19:19:03 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 19:19:03 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.30s | count: 1229
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 19:19:03 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 19:19:03 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 19:19:03 - App - INFO - ldconsole命令执行成功，输出长度: 48444
2025-07-27 19:19:03 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 19:19:03 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 19:19:03 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 19:19:03 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 19:19:03 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.30s | count: 1229
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 19:19:03 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 19:19:03 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 19:19:03 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 19:19:04 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-27 19:19:04 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-27 19:19:04 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-27 19:19:04 - __main__ - INFO - 后台服务已启动
2025-07-27 19:19:04 - __main__ - INFO - 延迟启动服务完成
2025-07-27 19:19:11 - MainWindowV2 - INFO - 开始重新扫描...
2025-07-27 19:19:11 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 手动刷新)
2025-07-27 19:19:11 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 19:19:11 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 19:19:11 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 19:19:11 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 19:19:11 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:19:11 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-27 19:19:11 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 19:19:11 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 19:19:11 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 19:19:11 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 19:19:11 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.31s | count: 1229
2025-07-27 19:19:11 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 19:19:11 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 19:19:11 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 19:19:11 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 19:19:12 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 19:19:12 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 19:19:12 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 19:19:12 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-27 19:19:16 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-27 19:19:16 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-27 19:19:16 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-27 19:19:16 - StartupManager - INFO - 启动调度器已启动
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-27 19:19:16 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 19:19:16 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 3
2025-07-27 19:19:16 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-27 19:19:16 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-27 19:19:16 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:19:16 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-27 19:19:16 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:19:16 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-27 19:19:16 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:19:16 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-27 19:19:16 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-27 19:19:16 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-27 19:19:16 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:19:16 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 3
2025-07-27 19:19:16 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:19:16 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/3)
2025-07-27 19:19:16 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 19:19:16 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-27 19:19:16 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-27 19:19:16 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-27 19:19:16 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:19:16 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-27 19:19:16 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-27 19:19:16 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/3)
2025-07-27 19:19:16 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-27 19:19:21 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:19:21 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:19:21 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:19:21 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-27 19:19:21 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-27 19:19:21 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-27 19:19:21 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/3)
2025-07-27 19:19:21 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-27 19:19:26 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.0秒
2025-07-27 19:19:26 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-27 19:19:26 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:19:26 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/3
2025-07-27 19:19:26 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-27 19:19:26 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-27 19:19:26 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-27 19:19:26 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-27 19:19:26 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-27 19:19:26 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:19:26 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:19:26 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-27 19:19:26 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-27 19:19:26 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-27 19:19:26 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 19:19:26 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-27 19:19:26 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/3)
2025-07-27 19:19:26 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 900 -> 3
2025-07-27 19:19:26 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 19:19:26 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:19:26 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 3
2025-07-27 19:19:27 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 12640
2025-07-27 19:19:27 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 19:19:27 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:19:27 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:19:27 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:19:27 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-27 19:19:27 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-27 19:19:27 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:19:27 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-27 19:19:27 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 3 -> 36
2025-07-27 19:19:27 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 19:19:27 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:19:27 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 36
2025-07-27 19:19:27 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-27 19:19:28 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 19:19:28 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 19:19:28 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 19:19:28 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 19:19:28 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 19:19:28 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 19:19:28 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 19:19:34 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-27 19:19:34 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-27 19:19:34 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-27 19:19:34 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-27 19:19:34 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-27 19:19:34 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-27 19:19:34 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-27 19:19:34 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-27 19:19:34 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 36秒，已运行: 0.00秒
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 19:19:35 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 13.6秒
2025-07-27 19:19:35 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-27 19:19:35 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/3
2025-07-27 19:19:35 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:19:35 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-27 19:19:35 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-27 19:19:35 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-27 19:19:35 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-27 19:19:35 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:19:35 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-27 19:19:35 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-27 19:19:35 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:19:35 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-27 19:19:35 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-27 19:19:35 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 19:19:35 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:19:35 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-27 19:19:35 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 16964
2025-07-27 19:19:35 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-27 19:19:35 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-27 19:19:37 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-27 19:19:37 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-27 19:19:37 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-27 19:19:38 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-27 19:19:38 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-27 19:19:40 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 19:19:40 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-27 19:19:40 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 13.5秒
2025-07-27 19:19:40 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 19:19:40 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:19:40 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/3
2025-07-27 19:19:40 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-27 19:19:40 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-27 19:19:40 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-27 19:19:40 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 3
2025-07-27 19:19:40 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-27 19:19:40 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:19:40 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:19:40 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-27 19:19:40 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-27 19:19:40 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-27 19:19:40 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 19:19:40 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 100.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:19:40 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-27 19:19:41 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 5684
2025-07-27 19:19:41 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 19:19:41 - StartupManager - INFO - 调度器已停止
2025-07-27 19:19:41 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-27 19:19:41 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-27 19:19:42 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-27 19:19:42 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 19:19:42 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 19:19:42 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 19:19:42 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-27 19:19:42 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-27 19:19:42 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 19:19:42 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 19:19:42 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 19:19:42 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 1)，保持不动
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 2): (604, 0) 保持原大小: 302x435
2025-07-27 19:19:42 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-27 19:19:42 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/3 个窗口
2025-07-27 19:19:42 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/3 个窗口 (成功: 1/3)
2025-07-27 19:19:43 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-27 19:19:43 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-27 19:19:43 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-27 19:19:43 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-27 19:19:43 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-27 19:19:43 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-27 19:19:43 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-27 19:19:43 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 36秒，已运行: 0.00秒
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-27 19:19:43 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-27 19:19:44 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-27 19:19:44 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-27 19:19:45 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 19:19:45 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-27 19:19:45 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-27 19:19:45 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-27 19:19:46 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-27 19:19:46 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-27 19:19:46 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-27 19:19:47 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-27 19:19:47 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-27 19:19:47 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-27 19:19:47 - InstagramDMTask - ERROR - [模拟器3] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-27 19:19:47 - InstagramDMTask - INFO - [模拟器3] 点击失败状态重置UI
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器3] 已点击失败状态，等待UI重置
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器3] 继续等待测试结果 (1/3)
2025-07-27 19:19:48 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-27 19:19:48 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-27 19:19:48 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-27 19:19:48 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-27 19:19:48 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-27 19:19:48 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-27 19:19:48 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-27 19:19:48 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-27 19:19:48 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 36秒，已运行: 0.00秒
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 267 毫秒
2025-07-27 19:19:49 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-27 19:19:51 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-27 19:19:52 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-27 19:19:52 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 19:19:53 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-27 19:19:54 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-27 19:19:54 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-27 19:19:54 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-27 19:19:54 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-27 19:19:55 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-27 19:19:55 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-27 19:19:55 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-27 19:19:56 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 连接成功：延时 278 毫秒
2025-07-27 19:19:56 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 278 毫秒
2025-07-27 19:19:56 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-27 19:19:56 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/3次
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-27 19:19:58 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-27 19:19:59 - InstagramDMTask - INFO - [模拟器5] 当前测试状态: 已连接，点击测试连接
2025-07-27 19:19:59 - InstagramDMTask - INFO - [模拟器5] 点击开始延迟测试
2025-07-27 19:20:00 - InstagramDMTask - INFO - [模拟器5] 已点击测试按钮，等待测试结果
2025-07-27 19:20:01 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-27 19:20:01 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-27 19:20:01 - InstagramDMTask - INFO - [模拟器5] 测试状态监控 (1/30): 连接成功：延时 292 毫秒
2025-07-27 19:20:01 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray节点延迟测试成功: 连接成功：延时 292 毫秒
2025-07-27 19:20:01 - InstagramDMTask - INFO - [模拟器5] 等待5秒后进入下一阶段
2025-07-27 19:20:02 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-27 19:20:02 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 19:20:02 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 19:20:02 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 19:20:02 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-27 19:20:02 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-27 19:20:02 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 19:20:02 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 19:20:02 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 19:20:02 - InstagramDMTask - INFO - [模拟器3] ❌ 批量验证失败
2025-07-27 19:20:02 - InstagramDMTask - INFO - [模拟器3] ❌ 验证失败
2025-07-27 19:20:04 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-27 19:20:04 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/3次
2025-07-27 19:20:04 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测中... 已等待6.4秒
2025-07-27 19:20:06 - InstagramDMTask - INFO - [模拟器5] 开始启动Instagram应用
2025-07-27 19:20:06 - InstagramDMTask - INFO - [模拟器5] Instagram启动命令执行成功，等待应用加载
2025-07-27 19:20:07 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-27 19:20:07 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始执行关注任务，模式: direct
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始执行【模式一：直接关注模式】
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: ryuche728_sn37 (1/2)
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：ryuche728_sn37
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：ryuche728_sn37
2025-07-27 19:20:07 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-27 19:20:08 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 ryuche728_sn37 的资料页
2025-07-27 19:20:08 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时1.53秒
2025-07-27 19:20:08 - InstagramFollowTask - WARNING - [模拟器3] 未找到关注按钮坐标
2025-07-27 19:20:08 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 1967毫秒
2025-07-27 19:20:09 - InstagramDMTask - INFO - [模拟器4] ✅ 批量验证成功
2025-07-27 19:20:09 - InstagramDMTask - INFO - [模拟器4] ✅ 验证成功
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始执行关注任务，模式: direct
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始执行【模式一：直接关注模式】
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始处理目标用户: momoca108 (1/2)
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始处理用户：momoca108
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 跳转到用户主页：momoca108
2025-07-27 19:20:09 - InstagramFollowTask - INFO - [模拟器4] 开始检测用户信息
2025-07-27 19:20:09 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram应用启动命令执行完成
2025-07-27 19:20:09 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测 第1/3次
2025-07-27 19:20:10 - InstagramFollowTask - INFO - [模拟器3] 开始处理目标用户: pon1125_ (2/2)
2025-07-27 19:20:10 - InstagramFollowTask - INFO - [模拟器3] 开始处理用户：pon1125_
2025-07-27 19:20:10 - InstagramFollowTask - INFO - [模拟器3] 跳转到用户主页：pon1125_
2025-07-27 19:20:10 - InstagramFollowTask - INFO - [模拟器3] 开始检测用户信息
2025-07-27 19:20:14 - InstagramFollowTask - INFO - [模拟器4] 🔍 关注状态检测: 关注
2025-07-27 19:20:14 - InstagramFollowTask - INFO - [模拟器4] ✅ 标题栏验证成功，当前在用户 momoca108 的资料页
2025-07-27 19:20:14 - InstagramFollowTask - INFO - [模拟器4] 用户信息检测完成，耗时5.53秒
2025-07-27 19:20:14 - InstagramFollowTask - INFO - [模拟器4] 该用户未关注,开始关注
2025-07-27 19:20:16 - InstagramFollowTask - INFO - [模拟器3] 🔍 关注状态检测: 关注
2025-07-27 19:20:16 - InstagramFollowTask - INFO - [模拟器3] ✅ 标题栏验证成功，当前在用户 pon1125_ 的资料页
2025-07-27 19:20:16 - InstagramFollowTask - INFO - [模拟器3] 用户信息检测完成，耗时5.34秒
2025-07-27 19:20:16 - InstagramFollowTask - INFO - [模拟器3] 该用户未关注,开始关注
2025-07-27 19:20:16 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待6.4秒
2025-07-27 19:20:17 - InstagramFollowTask - INFO - [模拟器4] 关注完成,已关注 1 / 2
2025-07-27 19:20:17 - InstagramFollowTask - INFO - [模拟器4] 直接关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-27 19:20:17 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 关注中 (1/2)
2025-07-27 19:20:17 - InstagramFollowTask - INFO - [模拟器4] ⏱️ 切换用户延迟: 1703毫秒
2025-07-27 19:20:18 - InstagramDMTask - INFO - [模拟器5] ✅ 批量验证成功
2025-07-27 19:20:18 - InstagramDMTask - INFO - [模拟器5] ✅ 验证成功
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始执行关注任务，模式: direct
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始执行【模式一：直接关注模式】
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: 2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] 该用户已关注,跳过 (1/2)
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始处理用户：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] 该用户已关注,跳过
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 跳转到用户主页：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] 该用户已关注,跳过
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器5] 开始检测用户信息
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器4] 开始处理目标用户: 2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] ⏱️ 切换用户延迟: 1892毫秒 (2/2)
2025-07-27 19:20:18 - InstagramFollowTask - INFO - [模拟器4] 开始处理用户：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] ⏱️ 切换用户延迟: 1892毫秒
2025-07-27 19:20:19 - InstagramFollowTask - INFO - [模拟器4] 跳转到用户主页：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器4] ⏱️ 切换用户延迟: 1892毫秒
2025-07-27 19:20:19 - InstagramFollowTask - INFO - [模拟器4] 开始检测用户信息
2025-07-27 19:20:19 - InstagramFollowTask - INFO - [模拟器3] 关注完成,已关注 1 / 2
2025-07-27 19:20:19 - InstagramFollowTask - INFO - [模拟器3] 直接关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-27 19:20:19 - InstagramFollowTask - INFO - [模拟器3] ⏱️ 切换用户延迟: 1685毫秒
2025-07-27 19:20:19 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 关注中 (1/2)
2025-07-27 19:20:20 - InstagramFollowTask - INFO - [模拟器4] 🔍 关注状态检测: 关注
2025-07-27 19:20:20 - InstagramFollowTask - INFO - [模拟器4] 用户信息检测完成，耗时1.54秒
2025-07-27 19:20:20 - InstagramFollowTask - ERROR - [模拟器4] 批量检测失败
2025-07-27 19:20:20 - InstagramFollowTask - INFO - [模拟器4] ⏱️ 切换用户延迟: 1606毫秒
2025-07-27 19:20:21 - InstagramFollowTask - INFO - [模拟器3] 总任务超时(36秒).任务进度: 1 / 2 耗时: 46.12秒
2025-07-27 19:20:21 - InstagramFollowTask - INFO - [模拟器3] 【模式一：直接关注循环】完成，成功关注: 1, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置观察者已注销
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] 开始任务完成后清理工作
2025-07-27 19:20:21 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] 准备关闭模拟器
2025-07-27 19:20:21 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 2
2025-07-27 19:20:21 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] 模拟器已成功关闭
2025-07-27 19:20:21 - InstagramDMTask - INFO - [模拟器3] 任务完成后清理工作完成
2025-07-27 19:20:21 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram直接关注任务执行成功
2025-07-27 19:20:21 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-27 19:20:21 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-27 19:20:21 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 19:20:21 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 19:20:21 - InstagramFollowTask - INFO - [模拟器5] 用户信息检测完成，耗时2.31秒
2025-07-27 19:20:21 - InstagramFollowTask - ERROR - [模拟器5] 批量检测失败
2025-07-27 19:20:21 - InstagramFollowTask - INFO - [模拟器5] ⏱️ 切换用户延迟: 1535毫秒
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器4] 总任务超时(36秒).任务进度: 1 / 2 耗时: 38.71秒
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器4] 【模式一：直接关注循环】完成，成功关注: 1, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置观察者已注销
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] 开始任务完成后清理工作
2025-07-27 19:20:22 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] 心跳监控已移除
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] 准备关闭模拟器
2025-07-27 19:20:22 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-27 19:20:22 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] 模拟器已成功关闭
2025-07-27 19:20:22 - InstagramDMTask - INFO - [模拟器4] 任务完成后清理工作完成
2025-07-27 19:20:22 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram直接关注任务执行成功
2025-07-27 19:20:22 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-27 19:20:22 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-27 19:20:22 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 19:20:22 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 19:20:22 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 19:20:22 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 19:20:22 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 19:20:22 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 19:20:22 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: 2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: future__39 (5/2) (2/2)
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器5] 开始处理用户：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: future__39 (5/2)
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器5] 跳转到用户主页：2025-07-27 19:13:28 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: future__39 (5/2)
2025-07-27 19:20:22 - InstagramFollowTask - INFO - [模拟器5] 开始检测用户信息
2025-07-27 19:20:24 - InstagramFollowTask - INFO - [模拟器5] 用户信息检测完成，耗时1.25秒
2025-07-27 19:20:24 - InstagramFollowTask - ERROR - [模拟器5] 批量检测失败
2025-07-27 19:20:24 - InstagramFollowTask - INFO - [模拟器5] ⏱️ 切换用户延迟: 1977毫秒
2025-07-27 19:20:26 - InstagramFollowTask - INFO - [模拟器5] 总任务超时(36秒).任务进度: 0 / 2 耗时: 37.22秒
2025-07-27 19:20:26 - InstagramFollowTask - INFO - [模拟器5] 【模式一：直接关注循环】完成，成功关注: 0, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置观察者已注销
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] 开始任务完成后清理工作
2025-07-27 19:20:26 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] 心跳监控已移除
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] 准备关闭模拟器
2025-07-27 19:20:26 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 0
2025-07-27 19:20:26 - Emulator - INFO - 模拟器停止成功 | emulator_id: 5
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] 模拟器已成功关闭
2025-07-27 19:20:26 - InstagramDMTask - INFO - [模拟器5] 任务完成后清理工作完成
2025-07-27 19:20:26 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务stop
2025-07-27 19:20:26 - MainWindowV2 - INFO - 模拟器5停止成功
2025-07-27 19:20:26 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 19:20:26 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram直接关注任务执行成功
2025-07-27 19:20:26 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 19:22:54 - MainWindowV2 - INFO - 开始重新扫描...
2025-07-27 19:22:54 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 手动刷新)
2025-07-27 19:22:54 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 19:22:54 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 19:22:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 19:22:54 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 19:22:54 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:22:54 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 19:22:54 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 19:22:54 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 19:22:54 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 19:22:54 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-27 19:22:54 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 19:22:54 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 19:22:54 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 19:22:54 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 19:22:54 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.22s | count: 1229
2025-07-27 19:22:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 19:22:54 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 19:22:54 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 19:22:54 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-27 19:22:58 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-27 19:22:58 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-27 19:22:58 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-27 19:22:58 - StartupManager - INFO - 启动调度器已启动
2025-07-27 19:22:58 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 19:22:58 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 3
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-27 19:22:58 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/3)
2025-07-27 19:22:58 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 19:22:58 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 3
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-27 19:22:58 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-27 19:22:58 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-27 19:22:58 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-27 19:22:58 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-27 19:22:58 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-27 19:22:58 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-27 19:22:58 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:22:58 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:22:58 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:22:58 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-27 19:22:58 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-27 19:22:58 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-27 19:22:58 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:22:58 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-27 19:22:58 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-27 19:22:58 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/3)
2025-07-27 19:22:58 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-27 19:23:03 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:23:03 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:23:03 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:23:03 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:23:03 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-27 19:23:03 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-27 19:23:03 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-27 19:23:03 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-27 19:23:03 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/3)
2025-07-27 19:23:03 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-27 19:23:08 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:23:08 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-27 19:23:08 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:23:08 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:23:08 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-27 19:23:08 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 19:23:08 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-27 19:23:08 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 3 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:23:08 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中3个, 运行0个, 失败0个 (并发槽位:3/3)
2025-07-27 19:23:08 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-27 19:23:11 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 13.2秒
2025-07-27 19:23:11 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-27 19:23:11 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-27 19:23:11 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:23:11 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/3
2025-07-27 19:23:11 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-27 19:23:11 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-27 19:23:11 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-27 19:23:11 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-27 19:23:11 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-27 19:23:11 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:23:11 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:23:11 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-27 19:23:11 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-27 19:23:11 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-27 19:23:11 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 19:23:11 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:23:11 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-27 19:23:11 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 21604
2025-07-27 19:23:11 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-27 19:23:13 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 19:23:13 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.2秒
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 19:23:13 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-27 19:23:13 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-27 19:23:13 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/3
2025-07-27 19:23:13 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-27 19:23:13 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-27 19:23:13 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:23:13 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 19:23:13 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 19:23:13 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:23:13 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-27 19:23:13 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-27 19:23:13 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:23:13 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-27 19:23:13 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-27 19:23:13 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 19:23:13 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:23:13 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-27 19:23:13 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 1592
2025-07-27 19:23:13 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-27 19:23:15 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-27 19:23:15 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-27 19:23:15 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-27 19:23:19 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-27 19:23:19 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-27 19:23:19 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-27 19:23:19 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-27 19:23:19 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-27 19:23:19 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-27 19:23:19 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-27 19:23:19 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 36秒，已运行: 0.00秒
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-27 19:23:19 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-27 19:23:20 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-27 19:23:21 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-27 19:23:21 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-27 19:23:21 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-27 19:23:21 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-27 19:23:21 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-27 19:23:21 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-27 19:23:21 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-27 19:23:21 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 36秒，已运行: 0.00秒
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-27 19:23:21 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-27 19:23:21 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 13.5秒
2025-07-27 19:23:21 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 19:23:21 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 19:23:21 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-27 19:23:21 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/3
2025-07-27 19:23:21 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-27 19:23:21 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 3
2025-07-27 19:23:21 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-27 19:23:21 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-27 19:23:21 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-27 19:23:21 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 19:23:21 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 19:23:21 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-27 19:23:21 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-27 19:23:21 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-27 19:23:21 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 19:23:21 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 100.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-27 19:23:21 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-27 19:23:22 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 14344
2025-07-27 19:23:22 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 19:23:22 - StartupManager - INFO - 调度器已停止
2025-07-27 19:23:22 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-27 19:23:22 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 19:23:22 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-27 19:23:22 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-27 19:23:22 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-27 19:23:22 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-27 19:23:22 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 19:23:22 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 19:23:22 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-27 19:23:22 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-27 19:23:22 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:23:22 - __main__ - INFO - 开始清理资源...
2025-07-27 19:23:22 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 19:23:22 - __main__ - INFO - 配置已保存
2025-07-27 19:23:22 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-27 19:23:22 - __main__ - INFO - 应用程序已退出
