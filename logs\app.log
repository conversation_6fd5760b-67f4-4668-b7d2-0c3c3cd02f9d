2025-07-27 17:11:33 - root - INFO - 简化日志系统初始化完成
2025-07-27 17:11:33 - App - INFO - 开始Instagram直接关注真实流程测试
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] ⚡ 开始Instagram直接关注快速测试
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 开始时间: 2025-07-27 17:11:33
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] ⚡ 快速模式：跳过阶段一、二、三，直接测试阶段四
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 开始设置真实测试环境
2025-07-27 17:11:33 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 17:11:33 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 17:11:33 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 17:11:33 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 17:11:33 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 17:11:33 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 17:11:33 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 17:11:33 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 17:11:33 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 17:11:33 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-27 17:11:33 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-27 17:11:33 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务配置加载完成
2025-07-27 17:11:33 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务执行器初始化完成
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 验证模拟器2就绪状态
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] ✅ 模拟器2就绪
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] ✅ 真实测试环境设置完成
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 开始测试阶段四: 直接关注业务流程
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 加载app_config.json真实配置
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 真实配置加载完成:
2025-07-27 17:11:33 - App - INFO - [直接关注测试器]   直接关注数量: 2
2025-07-27 17:11:33 - App - INFO - [直接关注测试器]   切换延迟: 1500-2000ms
2025-07-27 17:11:33 - App - INFO - [直接关注测试器]   关注延迟: 500-1000ms
2025-07-27 17:11:33 - App - INFO - [直接关注测试器]   地区筛选: 所有地区=False, 日本=True, 韩国=False, 泰国=False
2025-07-27 17:11:33 - App - INFO - [直接关注测试器] 🎯 开始执行模式一：直接关注模式（专用批量检测优化）
2025-07-27 17:11:33 - InstagramFollowTask - INFO - [模拟器2] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 17:11:33 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: ren__krn.0315 (1/2)
2025-07-27 17:11:33 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：ren__krn.0315
2025-07-27 17:11:34 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：ren__krn.0315
2025-07-27 17:11:34 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-27 17:11:39 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-27 17:11:39 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 ren__krn.0315 的资料页
2025-07-27 17:11:39 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时5.53秒
2025-07-27 17:11:39 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-27 17:11:42 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 1 / 2
2025-07-27 17:11:42 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-27 17:11:42 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 1890毫秒
2025-07-27 17:11:44 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: emipomgram (2/2)
2025-07-27 17:11:44 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：emipomgram
2025-07-27 17:11:44 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：emipomgram
2025-07-27 17:11:44 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-27 17:11:49 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-27 17:11:49 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 emipomgram 的资料页
2025-07-27 17:11:49 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时5.28秒
2025-07-27 17:11:49 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-27 17:11:52 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 2 / 2
2025-07-27 17:11:52 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务状态更新信号已发送: 已完成 (2/2)
2025-07-27 17:11:52 - InstagramFollowTask - INFO - [模拟器2] 任务完成,退出循环.--任务进度 :2 / 2 耗时: 18.98秒
2025-07-27 17:11:52 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务状态更新信号已发送: 已完成 (2/2)
2025-07-27 17:11:52 - InstagramFollowTask - INFO - [模拟器2] 【模式一：直接关注循环】完成，成功关注: 2, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] ✅ 阶段四测试通过
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 📊 执行统计:
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]   成功关注: 2
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]   跳过私密: 0
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]   跳过蓝V: 0
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] ================================================================================
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 📋 Instagram直接关注测试报告
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] ================================================================================
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 测试模式: ⚡ 快速模式
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 开始时间: 2025-07-27 17:11:33
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 结束时间: 2025-07-27 17:11:52
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 总耗时: 0:00:19.195512
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 总体结果: ✅ 通过
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] --------------------------------------------------------------------------------
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 直接关注业务流程: ✅ 通过 (17:11:52)
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]   📊 执行统计:
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]     total_user_followed: 2
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]     skipped_private: 0
2025-07-27 17:11:52 - App - INFO - [直接关注测试器]     skipped_verified: 0
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] ================================================================================
2025-07-27 17:11:52 - App - INFO - [直接关注测试器] 📄 测试报告已保存: test_report_direct_follow_2_20250727_171133.json
2025-07-27 17:11:52 - App - INFO - 🎉 Instagram直接关注测试完成 - 结果: ✅ 成功
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-27 17:13:09 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及2个模拟器
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-27 17:13:09 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 2
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 2
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [5, 6]
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-27 17:13:09 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-27 17:13:09 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-27 17:13:09 - StartupManager - INFO - 启动调度器已启动
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 17:13:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-27 17:13:09 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-27 17:13:09 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 17:13:09 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-27 17:13:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-27 17:13:09 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-27 17:13:09 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 17:13:09 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-27 17:13:09 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: direct
2025-07-27 17:13:09 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: direct
2025-07-27 17:13:09 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-27 17:13:09 - InstagramTaskManager - INFO - 启动模拟器5的Instagram任务线程 - 当前并发: 1/1
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-27 17:13:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 17:13:09 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 17:13:09 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-27 17:13:09 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-27 17:13:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 17:13:09 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 0.0秒
2025-07-27 17:13:09 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-27 17:13:09 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-27 17:13:09 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-27 17:13:09 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-27 17:13:09 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-27 17:13:09 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-27 17:13:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 17:13:09 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 17:13:09 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-27 17:13:11 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 2.0秒
2025-07-27 17:13:13 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 4.0秒
2025-07-27 17:13:15 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 6.0秒
2025-07-27 17:13:17 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 8.0秒
2025-07-27 17:13:19 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 10.0秒
2025-07-27 17:13:21 - InstagramTaskThread - INFO - [模拟器5] 等待启动中... 状态: 启动中, 已等待: 12.0秒
2025-07-27 17:13:22 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 13.0秒
2025-07-27 17:13:22 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 17:13:22 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-27 17:13:22 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-27 17:13:22 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 1
2025-07-27 17:13:22 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-27 17:13:22 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-27 17:13:22 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-27 17:13:22 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 17:13:22 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-27 17:13:22 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-27 17:13:22 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 17:13:22 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 17:13:22 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 17:13:22 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 15560
2025-07-27 17:13:22 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-27 17:13:23 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 14.0秒
2025-07-27 17:13:23 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-27 17:13:24 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 17:13:24 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 17:13:24 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 17:13:28 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:13:28 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:13:28 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:13:28 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:13:28 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:13:31 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-27 17:13:31 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-27 17:13:31 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-27 17:13:31 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-27 17:13:31 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-27 17:13:31 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-27 17:13:31 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-27 17:13:31 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 900秒，已运行: 0.00秒
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-27 17:13:31 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-27 17:13:32 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-27 17:13:32 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-27 17:13:35 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-27 17:13:35 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-27 17:13:36 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 17:13:36 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-27 17:13:37 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-27 17:13:37 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-27 17:13:38 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-27 17:13:41 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 17:13:41 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-27 17:13:41 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-27 17:13:41 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-27 17:13:42 - InstagramDMTask - INFO - [模拟器5] 当前测试状态: 已连接，点击测试连接
2025-07-27 17:13:42 - InstagramDMTask - INFO - [模拟器5] 点击开始延迟测试
2025-07-27 17:13:42 - InstagramDMTask - INFO - [模拟器5] 已点击测试按钮，等待测试结果
2025-07-27 17:13:43 - InstagramDMTask - INFO - [模拟器5] 测试状态监控 (1/30): 连接成功：延时 290 毫秒
2025-07-27 17:13:43 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray节点延迟测试成功: 连接成功：延时 290 毫秒
2025-07-27 17:13:43 - InstagramDMTask - INFO - [模拟器5] 等待5秒后进入下一阶段
2025-07-27 17:13:48 - InstagramDMTask - INFO - [模拟器5] 开始启动Instagram应用
2025-07-27 17:13:48 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:13:48 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:13:48 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:13:48 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:13:48 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:13:49 - InstagramDMTask - INFO - [模拟器5] Instagram启动命令执行成功，等待应用加载
2025-07-27 17:13:52 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram应用启动命令执行完成
2025-07-27 17:13:52 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测 第1/3次
2025-07-27 17:14:01 - InstagramDMTask - INFO - [模拟器5] ❌ 批量验证失败
2025-07-27 17:14:01 - InstagramDMTask - INFO - [模拟器5] ❌ 验证失败
2025-07-27 17:14:03 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待11.7秒
2025-07-27 17:14:06 - InstagramDMTask - INFO - [模拟器5] ❌ 批量验证失败
2025-07-27 17:14:06 - InstagramDMTask - INFO - [模拟器5] ❌ 验证失败
2025-07-27 17:14:08 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待16.5秒
2025-07-27 17:14:08 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:14:08 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:14:08 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:14:08 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:14:08 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:14:10 - InstagramDMTask - INFO - [模拟器5] ❌ 批量验证失败
2025-07-27 17:14:10 - InstagramDMTask - INFO - [模拟器5] ❌ 验证失败
2025-07-27 17:14:13 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待20.9秒
2025-07-27 17:14:17 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待25.0秒
2025-07-27 17:14:21 - InstagramDMTask - INFO - [模拟器5] ❌ 批量验证失败
2025-07-27 17:14:21 - InstagramDMTask - INFO - [模拟器5] ❌ 验证失败
2025-07-27 17:14:23 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测中... 已等待31.8秒
2025-07-27 17:14:27 - InstagramDMTask - INFO - [模拟器5] ✅ 批量验证成功
2025-07-27 17:14:27 - InstagramDMTask - INFO - [模拟器5] ✅ 验证成功
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始执行关注任务，模式: direct
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始执行【模式一：直接关注模式】
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: __nitamago__ (1/2)
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始处理用户：__nitamago__
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 跳转到用户主页：__nitamago__
2025-07-27 17:14:27 - InstagramFollowTask - INFO - [模拟器5] 开始检测用户信息
2025-07-27 17:14:28 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:14:28 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:14:28 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:14:28 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:14:28 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] 🔍 关注状态检测: 关注
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] ✅ 标题栏验证成功，当前在用户 __nitamago__ 的资料页
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] 🔍 蓝V检测: 找到认证标识
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] 用户信息检测完成，耗时5.61秒
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] 📊 目标用户处理结果: 蓝V认证账户
2025-07-27 17:14:32 - InstagramFollowTask - INFO - [模拟器5] ⏱️ 切换用户延迟: 1829毫秒
2025-07-27 17:14:34 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: maimai.k.s (2/2)
2025-07-27 17:14:34 - InstagramFollowTask - INFO - [模拟器5] 开始处理用户：maimai.k.s
2025-07-27 17:14:34 - InstagramFollowTask - INFO - [模拟器5] 跳转到用户主页：maimai.k.s
2025-07-27 17:14:34 - InstagramFollowTask - INFO - [模拟器5] 开始检测用户信息
2025-07-27 17:14:40 - InstagramFollowTask - INFO - [模拟器5] 🔍 关注状态检测: 关注
2025-07-27 17:14:40 - InstagramFollowTask - INFO - [模拟器5] ✅ 标题栏验证成功，当前在用户 maimai.k.s 的资料页
2025-07-27 17:14:40 - InstagramFollowTask - INFO - [模拟器5] 用户信息检测完成，耗时6.34秒
2025-07-27 17:14:40 - InstagramFollowTask - INFO - [模拟器5] 该用户未关注,开始关注
2025-07-27 17:14:44 - InstagramFollowTask - INFO - [模拟器5] 关注完成,已关注 1 / 2
2025-07-27 17:14:44 - InstagramFollowTask - INFO - [模拟器5] ⏱️ 切换用户延迟: 1883毫秒
2025-07-27 17:14:46 - InstagramFollowTask - INFO - [模拟器5] 开始处理目标用户: saki.mrjrsk (3/2)
2025-07-27 17:14:46 - InstagramFollowTask - INFO - [模拟器5] 开始处理用户：saki.mrjrsk
2025-07-27 17:14:46 - InstagramFollowTask - INFO - [模拟器5] 跳转到用户主页：saki.mrjrsk
2025-07-27 17:14:46 - InstagramFollowTask - INFO - [模拟器5] 开始检测用户信息
2025-07-27 17:14:48 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 17:14:48 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 17:14:48 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-27 17:14:48 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-27 17:14:48 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 17:14:51 - InstagramFollowTask - INFO - [模拟器5] 🔍 关注状态检测: 关注
2025-07-27 17:14:51 - InstagramFollowTask - INFO - [模拟器5] ✅ 标题栏验证成功，当前在用户 saki.mrjrsk 的资料页
2025-07-27 17:14:51 - InstagramFollowTask - INFO - [模拟器5] 用户信息检测完成，耗时5.33秒
2025-07-27 17:14:51 - InstagramFollowTask - INFO - [模拟器5] 该用户未关注,开始关注
2025-07-27 17:14:54 - InstagramFollowTask - INFO - [模拟器5] 关注完成,已关注 2 / 2
2025-07-27 17:14:54 - InstagramFollowTask - INFO - [模拟器5] 任务完成,退出循环.--任务进度 :2 / 2 耗时: 27.41秒
2025-07-27 17:14:54 - InstagramFollowTask - INFO - [模拟器5] 【模式一：直接关注循环】完成，成功关注: 2, 跳过蓝V: 1, 跳过私密: 0
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置观察者已注销
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] 开始任务完成后清理工作
2025-07-27 17:14:54 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] 心跳监控已移除
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] 准备关闭模拟器
2025-07-27 17:14:54 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 0
2025-07-27 17:14:54 - Emulator - INFO - 模拟器停止成功 | emulator_id: 5
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] 模拟器已成功关闭
2025-07-27 17:14:54 - InstagramDMTask - INFO - [模拟器5] 任务完成后清理工作完成
2025-07-27 17:14:54 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram直接关注任务执行成功
2025-07-27 17:14:54 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务stop
2025-07-27 17:14:54 - MainWindowV2 - INFO - 模拟器5停止成功
2025-07-27 17:14:54 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 17:14:54 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 17:14:55 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-27 17:14:55 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-27 17:14:55 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-27 17:14:55 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-27 17:14:55 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-27 17:14:55 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-27 17:14:55 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-27 17:14:55 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 17:14:55 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 17:14:55 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
