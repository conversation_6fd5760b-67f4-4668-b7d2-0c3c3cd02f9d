2025-07-27 18:34:24 - root - INFO - 简化日志系统初始化完成
2025-07-27 18:34:24 - App - INFO - 开始Instagram直接关注真实流程测试
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] ⚡ 开始Instagram直接关注快速测试
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 开始时间: 2025-07-27 18:34:24
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] ⚡ 快速模式：跳过阶段一、二、三，直接测试阶段四
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 开始设置真实测试环境
2025-07-27 18:34:24 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 18:34:24 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 18:34:24 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 18:34:24 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 18:34:24 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 18:34:24 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 18:34:24 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 18:34:24 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 18:34:24 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 18:34:24 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-27 18:34:24 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务配置加载完成
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务执行器初始化完成
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 验证模拟器2就绪状态
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] ✅ 模拟器2就绪
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] ✅ 真实测试环境设置完成
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 开始测试阶段四: 直接关注业务流程
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 加载app_config.json真实配置
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 真实配置加载完成:
2025-07-27 18:34:24 - App - INFO - [直接关注测试器]   直接关注数量: 2
2025-07-27 18:34:24 - App - INFO - [直接关注测试器]   切换延迟: 1500-2000ms
2025-07-27 18:34:24 - App - INFO - [直接关注测试器]   关注延迟: 500-1000ms
2025-07-27 18:34:24 - App - INFO - [直接关注测试器]   地区筛选: 所有地区=False, 日本=True, 韩国=False, 泰国=False
2025-07-27 18:34:24 - App - INFO - [直接关注测试器] 🎯 开始执行模式一：直接关注模式（专用批量检测优化）
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] 开始【模式一：直接关注循环】，目标关注数: 2
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: ryuche728_sn37 (1/2)
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：ryuche728_sn37
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：ryuche728_sn37
2025-07-27 18:34:24 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-27 18:34:26 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 ryuche728_sn37 的资料页
2025-07-27 18:34:26 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时1.25秒
2025-07-27 18:34:26 - InstagramFollowTask - WARNING - [模拟器2] 未找到关注按钮坐标
2025-07-27 18:34:26 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 1846毫秒
2025-07-27 18:34:27 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: momoca108 (2/2)
2025-07-27 18:34:27 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：momoca108
2025-07-27 18:34:28 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：momoca108
2025-07-27 18:34:28 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-27 18:34:32 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-27 18:34:32 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 momoca108 的资料页
2025-07-27 18:34:32 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时4.52秒
2025-07-27 18:34:32 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-27 18:34:34 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 1 / 2
2025-07-27 18:34:34 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 关注中 (1/2)
2025-07-27 18:34:34 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 1889毫秒
2025-07-27 18:34:36 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: pon1125_ (3/2)
2025-07-27 18:34:36 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：pon1125_
2025-07-27 18:34:36 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：pon1125_
2025-07-27 18:34:36 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-27 18:34:40 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时3.79秒
2025-07-27 18:34:40 - InstagramFollowTask - ERROR - [模拟器2] 批量检测失败
2025-07-27 18:34:40 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 1620毫秒
