2025-07-27 11:21:35 - root - INFO - 简化日志系统初始化完成
2025-07-27 11:21:35 - main - INFO - 应用程序启动
2025-07-27 11:21:35 - __main__ - INFO - Qt应用程序已创建
2025-07-27 11:21:35 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 11:21:35 - __main__ - INFO - 统一配置管理器已创建
2025-07-27 11:21:35 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 11:21:35 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 11:21:35 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 11:21:35 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 11:21:35 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 11:21:35 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 11:21:35 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 11:21:35 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 11:21:35 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 11:21:35 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 11:21:35 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-27 11:21:36 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 11:21:36 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-27 11:21:36 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-27 11:21:36 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-27 11:21:36 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-27 11:21:36 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-27 11:21:36 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-27 11:21:36 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-27 11:21:36 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-27 11:21:36 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-27 11:21:36 - __main__ - INFO - UI主窗口已创建
2025-07-27 11:21:36 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-27 11:21:36 - __main__ - INFO - 主窗口已显示
2025-07-27 11:21:36 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 11:21:36 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 11:21:36 - __main__ - INFO - UI层和业务层已连接
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 11:21:36 - __main__ - INFO - 启动Qt事件循环
2025-07-27 11:21:36 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 11:21:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:21:36 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 11:21:36 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 11:21:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:21:36 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 11:21:36 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 11:21:36 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 11:21:36 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 11:21:36 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 11:21:36 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 11:21:36 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 11:21:36 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 11:21:36 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 11:21:36 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 11:21:36 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 11:21:36 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 11:21:36 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 11:21:36 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.20s | count: 1229
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 11:21:36 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 11:21:36 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 11:21:36 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 11:21:37 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-27 11:21:37 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-27 11:21:37 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-27 11:21:37 - __main__ - INFO - 后台服务已启动
2025-07-27 11:21:37 - __main__ - INFO - 延迟启动服务完成
2025-07-27 11:22:31 - MainWindowV2 - INFO - 开始重新扫描...
2025-07-27 11:22:31 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 手动刷新)
2025-07-27 11:22:31 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-27 11:22:31 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-27 11:22:31 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-27 11:22:31 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-27 11:22:31 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:22:31 - App - INFO - ldconsole命令执行成功，输出长度: 48374
2025-07-27 11:22:31 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-27 11:22:31 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-27 11:22:31 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-27 11:22:31 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-27 11:22:31 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.22s | count: 1229
2025-07-27 11:22:31 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-27 11:22:31 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-27 11:22:31 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-27 11:22:31 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-27 11:22:33 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 11:22:33 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 11:22:33 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 11:22:33 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_fans_task
2025-07-27 11:22:34 - MainWindowV2 - INFO - Instagram关注粉丝任务已启动，涉及2个模拟器
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram关注粉丝任务请求
2025-07-27 11:22:34 - MainWindowV2 - INFO - 用户启动Instagram关注粉丝任务，模拟器数量: 2
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 开始处理Instagram关注粉丝任务（线程池模式），模拟器数量: 2
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [1, 2]
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-27 11:22:34 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-27 11:22:34 - StartupManager - INFO - 启动调度器已启动
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-27 11:22:34 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-27 11:22:34 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-27 11:22:34 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: fans
2025-07-27 11:22:34 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-27 11:22:34 - InstagramFollowTaskThread - INFO - [模拟器1] Instagram关注任务线程初始化完成，模式: fans
2025-07-27 11:22:34 - InstagramFollowTaskManager - INFO - 为模拟器 1 创建Instagram关注任务线程，模式: fans
2025-07-27 11:22:34 - InstagramFollowTaskThread - INFO - [模拟器2] Instagram关注任务线程初始化完成，模式: fans
2025-07-27 11:22:34 - InstagramFollowTaskManager - INFO - 为模拟器 2 创建Instagram关注任务线程，模式: fans
2025-07-27 11:22:34 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-27 11:22:34 - InstagramTaskManager - INFO - 启动模拟器1的Instagram任务线程 - 当前并发: 1/1
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步桥接器: Instagram关注粉丝任务请求已处理，状态: started
2025-07-27 11:22:34 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 11:22:34 - InstagramTaskThread - INFO - [模拟器1] 开始等待启动完成
2025-07-27 11:22:34 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器1状态变化: 排队中 -> 启动中
2025-07-27 11:22:34 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 0.0秒
2025-07-27 11:22:34 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 1
2025-07-27 11:22:34 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-27 11:22:34 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-27 11:22:34 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-27 11:22:34 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-27 11:22:34 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-27 11:22:34 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_fans_task
2025-07-27 11:22:34 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 排队中 | new_state: 启动中
2025-07-27 11:22:34 - MainWindowV2 - INFO - 模拟器1: 排队中 -> 启动中
2025-07-27 11:22:34 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 11:22:34 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 11:22:34 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 1
2025-07-27 11:22:36 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 2.0秒
2025-07-27 11:22:38 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 4.0秒
2025-07-27 11:22:40 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 6.0秒
2025-07-27 11:22:42 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 8.0秒
2025-07-27 11:22:44 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 10.0秒
2025-07-27 11:22:46 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 12.0秒
2025-07-27 11:22:48 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 14.0秒
2025-07-27 11:22:50 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 16.0秒
2025-07-27 11:22:52 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 18.0秒
2025-07-27 11:22:54 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 20.0秒
2025-07-27 11:22:56 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 22.0秒
2025-07-27 11:22:57 - Emulator - INFO - 强制关闭模拟器 | emulator_id: 1
2025-07-27 11:22:58 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 24.0秒
2025-07-27 11:22:59 - Emulator - INFO - 第 2/3 次启动尝试 | emulator_id: 1
2025-07-27 11:22:59 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 1
2025-07-27 11:23:00 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 26.0秒
2025-07-27 11:23:02 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 28.0秒
2025-07-27 11:23:04 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 30.0秒
2025-07-27 11:23:06 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 32.0秒
2025-07-27 11:23:08 - InstagramTaskThread - INFO - [模拟器1] 等待启动中... 状态: 启动中, 已等待: 34.0秒
2025-07-27 11:23:09 - Emulator - INFO - Android系统启动完成 | emulator_id: 1 | elapsed_time: 9.8秒
2025-07-27 11:23:09 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器1状态变化: 启动中 -> 运行中
2025-07-27 11:23:09 - Emulator - INFO - 模拟器状态变化 | emulator_id: 1 | old_state: 启动中 | new_state: 运行中
2025-07-27 11:23:09 - Emulator - INFO - 模拟器启动成功 | emulator_id: 1 | running_count: 1
2025-07-27 11:23:09 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已添加到任务活动监控，失败计数: 0
2025-07-27 11:23:09 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务start
2025-07-27 11:23:09 - MainWindowV2 - INFO - 模拟器1启动成功
2025-07-27 11:23:09 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 11:23:09 - WindowArrangementManager - INFO - 模拟器1启动完成，立即触发窗口排列
2025-07-27 11:23:09 - MainWindowV2 - WARNING - 未找到模拟器1，无法更新状态
2025-07-27 11:23:09 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 11:23:09 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 11:23:09 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 11:23:09 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中, PID: 10660
2025-07-27 11:23:09 - MainWindowV2 - INFO - 模拟器1: 启动中 -> 运行中
2025-07-27 11:23:10 - InstagramTaskThread - INFO - [模拟器1] ✅ 模拟器启动完成，等待时间: 36.0秒
2025-07-27 11:23:10 - InstagramTaskThread - INFO - [模拟器1] 开始窗口排列
2025-07-27 11:23:11 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-27 11:23:11 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-27 11:23:11 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-27 11:23:15 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 11:23:15 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 11:23:15 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 11:23:15 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 11:23:15 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 11:23:18 - InstagramTaskThread - INFO - [模拟器1] 窗口排列完成
2025-07-27 11:23:18 - InstagramFollowTaskThread - INFO - [模拟器1] 开始执行Instagram关注粉丝任务
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] 雷电模拟器API初始化成功
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] 已设置ld.emulator_id = 1
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置热加载观察者已注册
2025-07-27 11:23:18 - InstagramDMTask - INFO - [模拟器1] Instagram私信任务执行器初始化完成
2025-07-27 11:23:18 - InstagramFollowTask - INFO - [模拟器1] Instagram关注任务配置加载完成
2025-07-27 11:23:18 - InstagramFollowTask - INFO - [模拟器1] Instagram关注任务执行器初始化完成
2025-07-27 11:23:18 - InstagramFollowTask - INFO - [模拟器1] 关注模式已设置为: fans
2025-07-27 11:23:18 - InstagramFollowTask - INFO - [模拟器1] 开始执行Instagram关注任务
2025-07-27 11:23:18 - InstagramFollowTask - WARNING - [模拟器1] 任务开始时间未由线程传递，在此设置
2025-07-27 11:23:18 - InstagramFollowTask - INFO - [模拟器1] 任务超时设置: 66秒，已运行: 0.00秒
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] 模拟器Android系统运行正常，桌面稳定
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] 开始检查应用安装状态
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray已安装，版本: 1.1.12
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] 📊 应用安装状态检测结果:
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] ✅ 所有必要应用已安装
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] 开始启动V2Ray应用
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] V2Ray启动命令执行成功，等待应用加载
2025-07-27 11:23:19 - InstagramDMTask - INFO - [模拟器1] V2Ray应用启动结果: 成功
2025-07-27 11:23:22 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray应用启动成功
2025-07-27 11:23:22 - InstagramDMTask - INFO - [模拟器1] 开始检查V2Ray节点列表状态
2025-07-27 11:23:23 - InstagramDMTask - INFO - [模拟器1] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-27 11:23:23 - InstagramDMTask - INFO - [模拟器1] 开始连接V2Ray节点
2025-07-27 11:23:24 - InstagramDMTask - INFO - [模拟器1] 当前连接状态: 未连接
2025-07-27 11:23:24 - InstagramDMTask - INFO - [模拟器1] V2Ray节点未连接，开始连接
2025-07-27 11:23:26 - InstagramDMTask - INFO - [模拟器1] 已点击连接按钮，等待连接完成
2025-07-27 11:23:28 - InstagramDMTask - INFO - [模拟器1] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-27 11:23:28 - InstagramDMTask - INFO - [模拟器1] V2Ray节点连接成功
2025-07-27 11:23:28 - InstagramDMTask - INFO - [模拟器1] 开始测试V2Ray节点延迟
2025-07-27 11:23:28 - InstagramDMTask - INFO - [模拟器1] 开始V2Ray节点延迟测试
2025-07-27 11:23:29 - InstagramDMTask - INFO - [模拟器1] 当前测试状态: 已连接，点击测试连接
2025-07-27 11:23:29 - InstagramDMTask - INFO - [模拟器1] 点击开始延迟测试
2025-07-27 11:23:29 - InstagramDMTask - INFO - [模拟器1] 已点击测试按钮，等待测试结果
2025-07-27 11:23:31 - InstagramDMTask - INFO - [模拟器1] 测试状态监控 (1/30): 连接成功：延时 522 毫秒
2025-07-27 11:23:31 - InstagramDMTask - INFO - [模拟器1] ✅ V2Ray节点延迟测试成功: 连接成功：延时 522 毫秒
2025-07-27 11:23:31 - InstagramDMTask - INFO - [模拟器1] 等待5秒后进入下一阶段
2025-07-27 11:23:35 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 11:23:35 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 11:23:35 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 11:23:35 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 11:23:35 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 11:23:36 - InstagramDMTask - INFO - [模拟器1] 开始启动Instagram应用
2025-07-27 11:23:36 - InstagramDMTask - INFO - [模拟器1] Instagram启动命令执行成功，等待应用加载
2025-07-27 11:23:39 - InstagramDMTask - INFO - [模拟器1] ✅ Instagram应用启动命令执行完成
2025-07-27 11:23:39 - InstagramDMTask - INFO - [模拟器1] Instagram启动检测 第1/3次
2025-07-27 11:23:47 - InstagramDMTask - INFO - [模拟器1] ✅ 批量验证成功
2025-07-27 11:23:47 - InstagramDMTask - INFO - [模拟器1] ✅ 验证成功
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] Instagram页面状态检测结果: 正常-在主页面
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] ✅ Instagram已在主页面，可以继续执行任务
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 开始执行关注任务，模式: fans
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 开始执行【模式二：关注粉丝模式】
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 开始【模式二：关注粉丝循环】，目标关注数: 2
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 👥 开始处理目标用户: miyazaki_city (关注其粉丝)
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 开始处理用户：miyazaki_city
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] 跳转到用户主页：miyazaki_city
2025-07-27 11:23:47 - InstagramFollowTask - INFO - [模拟器1] ADB命令执行成功: Starting: Intent { act=android.intent.action.VIEW dat=instagram://user?username=miyazaki_city }
2025-07-27 11:23:52 - InstagramFollowTask - INFO - [模拟器1] ✅ 标题栏验证成功，当前在用户 miyazaki_city 的资料页
2025-07-27 11:23:52 - InstagramFollowTask - INFO - [模拟器1] 当前已在用户主页：miyazaki_city
2025-07-27 11:23:55 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 11:23:55 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 11:23:55 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 11:23:55 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 11:23:55 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 11:23:58 - InstagramFollowTask - INFO - [模拟器1] 🔍 蓝V检测: 未找到认证标识元素
2025-07-27 11:24:06 - InstagramFollowTask - INFO - [模拟器1] 成功打开粉丝列表
2025-07-27 11:24:06 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 找到 7 个粉丝容器
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.27秒，找到6个粉丝
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: Andy 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: 【公式】宮崎市上下水道局 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: 宮崎県小林市/木切倉整骨院/骨格矯正/産後矯正/交通事故/高周波EMS楽トレ 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: 居宅介護支援事業所まこと 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: Mari 🇮🇹 • Japan Travel 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 用户: みやざきに市民ファンドを！ 不符合地区筛选,跳过
2025-07-27 11:24:07 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 11:24:08 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.63秒
2025-07-27 11:24:08 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 11:24:09 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 11:24:09 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.27秒，找到6个粉丝
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: みやざきに市民ファンドを！ 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: タノサウナ 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: 株式会社トランス・グリップ 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: みやふぁむ【宮崎グルメ/観光】 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: み 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 用户: シェアハウス Emoa 不符合地区筛选,跳过
2025-07-27 11:24:10 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 11:24:11 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.65秒
2025-07-27 11:24:11 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 11:24:12 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 11:24:12 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 成功提取 5 个粉丝信息
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：5个粉丝，滚动5个位置，距离280px
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.36秒，找到5个粉丝
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 用户: 合同会社 S.F 不符合地区筛选,跳过
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 用户: 南パンキッチンカー＠都城 【キッチンカー専用🚚】 不符合地区筛选,跳过
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 用户: 宮崎江南病院 不符合地区筛选,跳过
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 用户: Gato 不符合地区筛选,跳过
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 用户: hana 不符合地区筛选,跳过
2025-07-27 11:24:13 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 11:24:14 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动5个位置，距离280px，耗时0.62秒
2025-07-27 11:24:14 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 11:24:15 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 11:24:15 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:15 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-27 11:24:15 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-27 11:24:15 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 1
2025-07-27 11:24:15 - MainWindowV2 - WARNING - 模拟器1心跳状态更新未产生变化
2025-07-27 11:24:15 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 成功提取 7 个粉丝信息
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：7个粉丝，滚动7个位置，距离350px
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.45秒，找到7个粉丝
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: hana 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: riku.i 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: KOU 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 英文, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: sa 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: サトウミドリ 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 其他, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: 参政党(公認)宮崎県支部連合会🟠🌸 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 用户: の 不符合地区筛选,跳过
2025-07-27 11:24:16 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 11:24:17 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动7个位置，距离350px，耗时0.63秒
2025-07-27 11:24:17 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 11:24:18 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 11:24:18 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:19 - InstagramFollowTask - INFO - [模拟器1] 找到 2 个粉丝容器
2025-07-27 11:24:19 - InstagramFollowTask - INFO - [模拟器1] 成功提取 0 个粉丝信息
2025-07-27 11:24:19 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：4个粉丝，滚动4个位置，距离280px
2025-07-27 11:24:19 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时1.32秒，找到0个粉丝
2025-07-27 11:24:19 - InstagramFollowTask - INFO - [模拟器1] 发现查看更多按钮，点击成功
2025-07-27 11:24:21 - InstagramFollowTask - INFO - [模拟器1] 开始批量检测所有UI元素
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 找到 8 个粉丝容器
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 成功提取 6 个粉丝信息
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动参数：6个粉丝，滚动6个位置，距离300px
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 批量检测完成，耗时2.73秒，找到6个粉丝
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: トミー 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: 宮崎県ボディビル・フィットネス連盟 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: けんた 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: 【公式】体操女子✨️のともん🤸‍♀️ 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: Masayasu Morita AKA もりたま 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 当前地区筛选配置: ['泰国']
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 🔍 语言检测: 日文, 筛选结果: False
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 用户: Flight Base 新田原　(空の駅竜馬) 不符合地区筛选,跳过
2025-07-27 11:24:24 - InstagramFollowTask - INFO - [模拟器1] 开始使用预计算参数滚动
2025-07-27 11:24:25 - InstagramFollowTask - INFO - [模拟器1] 预计算滚动完成，滑动6个位置，距离300px，耗时0.63秒
2025-07-27 11:24:25 - InstagramFollowTask - INFO - [模拟器1] 步骤9：滚动完成，开始等待1秒让页面稳定加载
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] 步骤9：1秒等待完成，准备下一轮循环
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] 总任务超时(66秒)，退出
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] 📊 目标用户 miyazaki_city 处理结果: 总任务超时(66秒)，退出
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] ⏱️ 切换用户延迟: 652毫秒
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] 总任务超时(66秒).任务进度: 0 / 2 耗时: 68.09秒
2025-07-27 11:24:26 - InstagramFollowTask - INFO - [模拟器1] ✅ 【模式二：关注粉丝循环】完成，成功关注: 0, 跳过蓝V: 0, 跳过私密: 0
2025-07-27 11:24:26 - InstagramDMTask - INFO - [模拟器1] Instagram任务配置观察者已注销
2025-07-27 11:24:26 - InstagramDMTask - INFO - [模拟器1] 开始任务完成后清理工作
2025-07-27 11:24:26 - TaskActivityHeartbeatManager - INFO - 模拟器 1 已从任务活动监控移除
2025-07-27 11:24:26 - InstagramDMTask - INFO - [模拟器1] 心跳监控已移除
2025-07-27 11:24:26 - InstagramDMTask - INFO - [模拟器1] 准备关闭模拟器
2025-07-27 11:24:27 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 1 | remaining_running: 0
2025-07-27 11:24:27 - Emulator - INFO - 模拟器停止成功 | emulator_id: 1
2025-07-27 11:24:27 - InstagramDMTask - INFO - [模拟器1] 模拟器已成功关闭
2025-07-27 11:24:27 - InstagramDMTask - INFO - [模拟器1] 任务完成后清理工作完成
2025-07-27 11:24:27 - InstagramFollowTaskThread - INFO - [模拟器1] Instagram关注粉丝任务执行成功
2025-07-27 11:24:27 - MainWindowV2 - INFO - 任务完成: 模拟器1, 任务stop
2025-07-27 11:24:27 - MainWindowV2 - INFO - 模拟器1停止成功
2025-07-27 11:24:27 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=1, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-27 11:24:27 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-27 11:24:27 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 11:24:27 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 排队中 | new_state: 启动中
2025-07-27 11:24:27 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器2状态变化: 排队中 -> 启动中
2025-07-27 11:24:27 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 2
2025-07-27 11:24:27 - MainWindowV2 - INFO - 模拟器2: 排队中 -> 启动中
2025-07-27 11:24:27 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 11:24:27 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-27 11:24:28 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 2
2025-07-27 11:24:35 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 66 -> 9
2025-07-27 11:24:35 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 11:24:35 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:24:35 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 9
2025-07-27 11:24:36 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 9 -> 90
2025-07-27 11:24:36 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 11:24:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:24:36 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 90
2025-07-27 11:24:36 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 90 -> 900
2025-07-27 11:24:36 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-27 11:24:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-27 11:24:36 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 900
2025-07-27 11:24:37 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-27 11:24:37 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-27 11:24:37 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-27 11:24:37 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-27 11:24:37 - Emulator - INFO - Android系统启动完成 | emulator_id: 2 | elapsed_time: 9.9秒
2025-07-27 11:24:37 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器2状态变化: 启动中 -> 运行中
2025-07-27 11:24:37 - Emulator - INFO - 模拟器状态变化 | emulator_id: 2 | old_state: 启动中 | new_state: 运行中
2025-07-27 11:24:37 - InstagramFollowTaskManager - INFO - 启动模拟器2的Instagram关注任务线程 - 当前并发: 2/1
2025-07-27 11:24:37 - TaskActivityHeartbeatManager - INFO - 模拟器 2 已添加到任务活动监控，失败计数: 0
2025-07-27 11:24:37 - Emulator - INFO - 模拟器启动成功 | emulator_id: 2 | running_count: 1
2025-07-27 11:24:37 - MainWindowV2 - INFO - 任务完成: 模拟器2, 任务start
2025-07-27 11:24:37 - MainWindowV2 - INFO - 模拟器2启动成功
2025-07-27 11:24:37 - InstagramTaskThread - INFO - [模拟器2] 开始等待启动完成
2025-07-27 11:24:37 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=2, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-27 11:24:37 - InstagramTaskThread - INFO - [模拟器2] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-27 11:24:37 - InstagramTaskThread - INFO - [模拟器2] 开始窗口排列
2025-07-27 11:24:37 - WindowArrangementManager - INFO - 模拟器2启动完成，立即触发窗口排列
2025-07-27 11:24:37 - MainWindowV2 - WARNING - 未找到模拟器2，无法更新状态
2025-07-27 11:24:37 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
2025-07-27 11:24:37 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-27 11:24:37 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-27 11:24:37 - StartupManager - INFO - 调度器已停止
2025-07-27 11:24:38 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中, PID: 7448
2025-07-27 11:24:38 - MainWindowV2 - INFO - 模拟器2: 启动中 -> 运行中
