2025-07-27 19:16:56 - root - INFO - 简化日志系统初始化完成
2025-07-27 19:16:56 - App - INFO - ================================================================================
2025-07-27 19:16:56 - App - INFO - 私密账户检测专项测试开始
2025-07-27 19:16:56 - App - INFO - ================================================================================
2025-07-27 19:16:56 - App - INFO - [测试器] 设置测试环境，模拟器ID: 2
2025-07-27 19:16:56 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-27 19:16:56 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-27 19:16:56 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-27 19:16:56 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-27 19:16:56 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-27 19:16:56 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-27 19:16:56 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-27 19:16:56 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-27 19:16:56 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-27 19:16:56 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-27 19:16:56 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-27 19:16:56 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务配置加载完成
2025-07-27 19:16:56 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务执行器初始化完成
2025-07-27 19:16:56 - App - INFO - [测试器] ✅ 测试环境设置完成
2025-07-27 19:16:56 - App - INFO - [测试器] 🔍 开始测试私密账户检测功能
2025-07-27 19:16:57 - InstagramFollowTask - INFO - [模拟器2] 🔍 检测到私密账户标识: '这是私密帐户'
2025-07-27 19:16:57 - App - INFO - [测试器] 🔍 私密账户检测结果: True
2025-07-27 19:16:57 - App - INFO - [测试器] 📄 调试XML文件已生成: debug_xml_2.xml
2025-07-27 19:16:57 - App - INFO - [测试器] 📄 XML文件大小: 32010 字节
2025-07-27 19:16:57 - App - INFO - [测试器] 📄 XML内容预览: '<?xml version=\'1.0\' encoding=\'UTF-8\' standalone=\'yes\' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.instagram.android" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][260,400]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.instagram'
2025-07-27 19:16:57 - App - INFO - ================================================================================
2025-07-27 19:16:57 - App - INFO - 私密账户检测专项测试完成
2025-07-27 19:16:57 - App - INFO - 检测结果: True
2025-07-27 19:16:57 - App - INFO - ================================================================================
